# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* data_recycle
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# MichaelHadar, 2024
# or balmas, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: or balmas, 2025\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.notification
msgid ""
"' recycling rule.<br/>\n"
"You can validate those changes"
msgstr ""
"' כלל מיחזור.<br/>\n"
"אתה יכול לאשר את השינויים האלו"

#. module: data_recycle
#. odoo-python
#: code:addons/data_recycle/models/data_recycle_record.py:0
msgid "**Record Deleted**"
msgstr "**רשומה נמחקה**"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_merge_model_form
msgid "<span class=\"me-1\">Every</span>"
msgstr "<span class=\"me-1\">כל</span>"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__active
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__active
msgid "Active"
msgstr "פעיל"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__recycle_action__archive
msgid "Archive"
msgstr "ארכיון"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__recycle_mode__automatic
msgid "Automatic"
msgstr "אוטומטי"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__company_id
msgid "Company"
msgstr "חברה"

#. module: data_recycle
#: model:ir.ui.menu,name:data_recycle.menu_data_cleaning_config
msgid "Configuration"
msgstr "תצורה"

#. module: data_recycle
#: model_terms:ir.actions.act_window,help:data_recycle.action_data_recycle_record
#: model_terms:ir.actions.act_window,help:data_recycle.action_data_recycle_record_notification
msgid "Configure rules to identify records to clean"
msgstr "הגדרת כללים לזיהוי רשומות לנקיון"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__create_uid
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__create_date
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: data_recycle
#: model:ir.ui.menu,name:data_recycle.menu_data_cleaning_root
msgid "Data Cleaning"
msgstr "ניקוי נתונים"

#. module: data_recycle
#: model:ir.actions.server,name:data_recycle.ir_cron_clean_records_ir_actions_server
msgid "Data Recycle: Clean Records"
msgstr "מחזור נתונים: מחיקת רשומות"

#. module: data_recycle
#. odoo-python
#: code:addons/data_recycle/models/data_recycle_model.py:0
msgid "Data to Recycle"
msgstr "נתונים למחזור"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__notify_frequency_period__days
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__time_field_delta_unit__days
msgid "Days"
msgstr "ימים"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__recycle_action__unlink
msgid "Delete"
msgstr "מחיקה"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__time_field_delta
msgid "Delta"
msgstr ""

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__time_field_delta_unit
msgid "Delta Unit"
msgstr ""

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_recycle_record_list
msgid "Discard"
msgstr "בטל"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_recycle_record_search
msgid "Discarded"
msgstr "בוטל"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__display_name
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: data_recycle
#: model:ir.actions.act_window,name:data_recycle.action_data_recycle_record
#: model:ir.actions.act_window,name:data_recycle.action_data_recycle_record_notification
msgid "Field Recycle Records"
msgstr ""

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__domain
msgid "Filter"
msgstr "מסנן"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__id
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__id
msgid "ID"
msgstr "מזהה"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__include_archived
msgid "Include Archived"
msgstr "כלול בארכיון"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__last_notification
msgid "Last Notification"
msgstr "התראה אחרונה"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__write_uid
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__write_date
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: data_recycle
#: model:ir.model.fields,help:data_recycle.field_data_recycle_model__notify_user_ids
msgid "List of users to notify when there are new records to recycle"
msgstr "רשימת משתמשים שיש להודיע להם כאשר יש רשומות חדשות למיחזור"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__recycle_mode__manual
msgid "Manual"
msgstr "ידני"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__res_model_id
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__res_model_id
msgid "Model"
msgstr "מודל"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__res_model_name
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__res_model_name
msgid "Model Name"
msgstr "שם מודל"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__notify_frequency_period__months
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__time_field_delta_unit__months
msgid "Months"
msgstr "חודשים"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__name
msgid "Name"
msgstr "שם"

#. module: data_recycle
#: model_terms:ir.actions.act_window,help:data_recycle.action_data_recycle_record
#: model_terms:ir.actions.act_window,help:data_recycle.action_data_recycle_record_notification
msgid "No cleaning suggestions"
msgstr "אין הצעות לנקיון"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__notify_frequency
msgid "Notify"
msgstr "להודיע"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__notify_frequency_period
msgid "Notify Frequency Period"
msgstr "תדירות הודעות"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__notify_user_ids
msgid "Notify Users"
msgstr "הודעה למשתמשים"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__res_id
msgid "Record ID"
msgstr "מזהה רשומה"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__name
msgid "Record Name"
msgstr "שם רשומה"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_merge_model_form
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_recycle_record_search
msgid "Records"
msgstr "רשומות"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__records_to_recycle_count
msgid "Records To Recycle"
msgstr "רשומות למיחזור"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__recycle_action
msgid "Recycle Action"
msgstr "פעולת מיחזור"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__recycle_mode
msgid "Recycle Mode"
msgstr "מצב מיחזור"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__recycle_model_id
msgid "Recycle Model"
msgstr "מודול מיחזור"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__recycle_record_ids
msgid "Recycle Record"
msgstr "רשומת מיחזור"

#. module: data_recycle
#: model:ir.ui.menu,name:data_recycle.menu_data_cleaning_config_rules_recycle
#: model:ir.ui.menu,name:data_recycle.menu_data_recycle_record
msgid "Recycle Records"
msgstr "רשומות מיחזור"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_recycle_record_list
msgid "Recycle Rule"
msgstr "כלל מיחזור"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_recycle_record_search
msgid "Recycle Rules"
msgstr "כללי מיחזור"

#. module: data_recycle
#: model:ir.model,name:data_recycle.model_data_recycle_model
msgid "Recycling Model"
msgstr "מודול מיחזור"

#. module: data_recycle
#: model:ir.model,name:data_recycle.model_data_recycle_record
msgid "Recycling Record"
msgstr "ממחזר רשומה"

#. module: data_recycle
#: model:ir.actions.act_window,name:data_recycle.action_data_recycle_config
msgid "Recyle Records Rules"
msgstr "כללי מיחזור רשומות "

#. module: data_recycle
#: model:ir.ui.menu,name:data_recycle.menu_data_cleaning_config_rules
msgid "Rules"
msgstr "כללים"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_merge_model_form
msgid "Run Now"
msgstr "הרץ עכשיו"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_merge_model_form
msgid "Select a model to configure recycling actions"
msgstr "בחר מודול להגדרת פעולות מיחזור"

#. module: data_recycle
#: model:ir.model.constraint,message:data_recycle.constraint_data_recycle_model_check_notif_freq
msgid "The notification frequency should be greater than 0"
msgstr "תדירות ההודעות צריכה להיות יותר מ-0"

#. module: data_recycle
#. odoo-python
#: code:addons/data_recycle/models/data_recycle_model.py:0
msgid "This model doesn't manage archived records. Only deletion is possible."
msgstr "מודל זה לא מנהל רשומות בארכיון. ניתן רק למחוק."

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__time_field_id
msgid "Time Field"
msgstr "שדה זמן"

#. module: data_recycle
#. odoo-python
#: code:addons/data_recycle/models/data_recycle_record.py:0
msgid "Undefined Name"
msgstr "שם לא מוגדר"

#. module: data_recycle
#. odoo-javascript
#: code:addons/data_recycle/static/src/views/data_recycle_list_view.xml:0
msgid "Unselect"
msgstr "הסרת בחירה"

#. module: data_recycle
#. odoo-javascript
#: code:addons/data_recycle/static/src/views/data_recycle_list_view.xml:0
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_recycle_record_list
msgid "Validate"
msgstr "אשר"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.notification
msgid "We've identified"
msgstr "זיהינו"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__notify_frequency_period__weeks
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__time_field_delta_unit__weeks
msgid "Weeks"
msgstr "שבועות"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__time_field_delta_unit__years
msgid "Years"
msgstr "שנים"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.notification
msgid "here"
msgstr "כאן"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.notification
msgid "records to clean with the '"
msgstr "רשומות לנקות עם ה'"
