# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_product
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# <PERSON><PERSON><PERSON><PERSON>ak, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-29 00:00+0000\n"
"Last-Translator: Ujja<PERSON> Pathak, 2025\n"
"Language-Team: Hindi (https://app.transifex.com/odoo/teams/41243/hi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: event_product
#: model:ir.model.fields,help:event_product.field_event_event_ticket__description
#: model:ir.model.fields,help:event_product.field_event_type_ticket__description
msgid ""
"A description of the ticket that you want to communicate to your customers."
msgstr ""

#. module: event_product
#: model:ir.model.fields,field_description:event_product.field_product_product__service_tracking
#: model:ir.model.fields,field_description:event_product.field_product_template__service_tracking
msgid "Create on Order"
msgstr ""

#. module: event_product
#: model:ir.model.fields,field_description:event_product.field_event_event__currency_id
#: model:ir.model.fields,field_description:event_product.field_event_event_ticket__currency_id
#: model:ir.model.fields,field_description:event_product.field_event_type_ticket__currency_id
msgid "Currency"
msgstr "करेंसी"

#. module: event_product
#: model:ir.model.fields,field_description:event_product.field_event_event_ticket__description
#: model:ir.model.fields,field_description:event_product.field_event_type_ticket__description
msgid "Description"
msgstr "विवरण"

#. module: event_product
#: model:ir.model,name:event_product.model_event_event
msgid "Event"
msgstr ""

#. module: event_product
#: model:ir.model.fields.selection,name:event_product.selection__product_template__service_tracking__event
msgid "Event Registration"
msgstr ""

#. module: event_product
#: model:product.template,name:event_product.product_product_event_standard_product_template
msgid "Event Registration - Standard"
msgstr ""

#. module: event_product
#: model:product.template,name:event_product.product_product_event_vip_product_template
msgid "Event Registration - VIP"
msgstr ""

#. module: event_product
#: model:ir.model,name:event_product.model_event_type_ticket
msgid "Event Template Ticket"
msgstr ""

#. module: event_product
#: model:ir.model,name:event_product.model_event_event_ticket
msgid "Event Ticket"
msgstr ""

#. module: event_product
#: model:ir.model.fields,field_description:event_product.field_product_product__event_ticket_ids
msgid "Event Tickets"
msgstr ""

#. module: event_product
#: model:ir.model.fields,field_description:event_product.field_event_event_ticket__price
#: model:ir.model.fields,field_description:event_product.field_event_type_ticket__price
msgid "Price"
msgstr "कीम"

#. module: event_product
#: model:ir.model.fields,field_description:event_product.field_event_event_ticket__price_reduce
#: model:ir.model.fields,field_description:event_product.field_event_type_ticket__price_reduce
msgid "Price Reduce"
msgstr ""

#. module: event_product
#: model:ir.model.fields,field_description:event_product.field_event_event_ticket__price_reduce_taxinc
msgid "Price Reduce Tax inc"
msgstr ""

#. module: event_product
#: model:ir.model.fields,field_description:event_product.field_event_event_ticket__price_incl
msgid "Price include"
msgstr ""

#. module: event_product
#: model:ir.model,name:event_product.model_product_template
#: model:ir.model.fields,field_description:event_product.field_event_event_ticket__product_id
#: model:ir.model.fields,field_description:event_product.field_event_type_ticket__product_id
msgid "Product"
msgstr "उत्पाद"

#. module: event_product
#: model:ir.model,name:event_product.model_product_product
msgid "Product Variant"
msgstr "उत्पाद प्रकार"

#. module: event_product
#: model_terms:ir.ui.view,arch_db:event_product.event_event_ticket_view_tree_from_event
msgid "Sales End"
msgstr ""

#. module: event_product
#: model_terms:ir.ui.view,arch_db:event_product.event_event_ticket_view_tree_from_event
msgid "Sales Start"
msgstr ""
