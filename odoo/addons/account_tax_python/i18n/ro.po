# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_tax_python
# 
# Translators:
# Wil Odoo, 2024
# Larisa_nexterp, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Larisa_nexterp, 2025\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__amount_type
msgid ""
"\n"
"    - Group of Taxes: The tax is a set of sub taxes.\n"
"    - Fixed: The tax amount stays the same whatever the price.\n"
"    - Percentage: The tax amount is a % of the price:\n"
"        e.g 100 * (1 + 10%) = 110 (not price included)\n"
"        e.g 110 / (1 + 10%) = 100 (price included)\n"
"    - Percentage Tax Included: The tax amount is a division of the price:\n"
"        e.g 180 / (1 - 10%) = 200 (not price included)\n"
"        e.g 200 * (1 - 10%) = 180 (price included)\n"
"        "
msgstr ""
"\n"
"    - Grup de taxe: Taxa este un set de sub-taxe.\n"
"    - Fix: Suma taxei rămâne aceeași indiferent de preț.\n"
"    - Procent din preț: Valoarea taxei este un procent din preț:\n"
"        ex: 100 * (1 + 10%) = 110 (prețul nu este inclus)\n"
"        ex: 110 / (1 + 10%) = 100 (preț inclus)\n"
"    - Procent inclus în preț: Valoarea taxei este o împărțire a prețului:\n"
"        ex: 180 / (1 - 10%) = 200 (prețul nu este inclus)\n"
"        ex: 200 * (1 - 10%) = 180 (preț inclus)\n"
"        "

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__formula
msgid ""
"Compute the amount of the tax.\n"
"\n"
":param base: float, actual amount on which the tax is applied\n"
":param price_unit: float\n"
":param quantity: float\n"
":param product: A object representing the product\n"
msgstr ""
"Calculează valoarea taxei.\n"
"\n"
":param base: float, suma efectivă la care se aplică taxa\n"
":param price_unit: float\n"
":param quantity: float\n"
":param product: Un obiect care reprezintă produsul\n"

#. module: account_tax_python
#: model:ir.model.fields.selection,name:account_tax_python.selection__account_tax__amount_type__code
msgid "Custom Formula"
msgstr "Formulă personalizată"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__formula
msgid "Formula"
msgstr "Formulă"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__formula_decoded_info
msgid "Formula Decoded Info"
msgstr "Informații decodificate formulă"

#. module: account_tax_python
#. odoo-python
#: code:addons/account_tax_python/models/account_tax.py:0
msgid "Malformed formula '%(formula)s' at position %(position)s"
msgstr "Formulă incorectă '%(formula)s' la poziția %(position)s"

#. module: account_tax_python
#: model:ir.model,name:account_tax_python.model_account_tax
msgid "Tax"
msgstr "Taxă"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__amount_type
msgid "Tax Computation"
msgstr "Calculul taxei"
