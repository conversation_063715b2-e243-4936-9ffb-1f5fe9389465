# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_booth
# 
# Translators:
# <PERSON><PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_booked_template
msgid "<b>Ren<PERSON>ail</b>:"
msgstr "<b>Lejers e-mailadresse</b>:"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_booked_template
msgid "<b>Renter Name</b>:"
msgstr "<b>Lejers navn</b>:"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_booked_template
msgid "<b>Renter Phone</b>:"
msgstr "<b>Lejers telefonnummer</b>:"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_event_view_form
msgid "<span class=\"o_stat_text\">Booths</span>"
msgstr "<span class=\"o_stat_text\">Stande</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
#: model_terms:event.booth.category,description:event_booth.event_booth_category_standard
msgid "<span>1 Branded Booth</span>"
msgstr "<span>1 Stand med branding</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
#: model_terms:event.booth.category,description:event_booth.event_booth_category_standard
msgid "<span>1 desk</span>"
msgstr "<span>1 skrivebord</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>10 + 1 passes</span>"
msgstr "<span>10 + 1 adgangspas</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>100 words description on website</span>"
msgstr "<span>Beskrivelse på 100 ord på hjemmesiden</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>2 Branded Booth</span>"
msgstr "<span>2 Stand med branding</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>2 desks</span>"
msgstr "<span>2 skriveborde</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>2 x 46\" display screens</span>"
msgstr "<span>2 x 46\" skærme</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
#: model_terms:event.booth.category,description:event_booth.event_booth_category_standard
msgid "<span>46\" display screen</span>"
msgstr "<span>46\" skærme</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
#: model_terms:event.booth.category,description:event_booth.event_booth_category_standard
msgid "<span>4m²</span>"
msgstr "<span>4m²</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
msgid "<span>50 words description on website</span>"
msgstr "<span>50 ords beskrivelse på hjemmeside</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>8m²</span>"
msgstr "<span>8m²</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
#: model_terms:event.booth.category,description:event_booth.event_booth_category_standard
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>Logo &amp; link on website</span>"
msgstr "<span>Logo &amp; link på hjemmesiden</span>"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_needaction
msgid "Action Needed"
msgstr "Handling påkrævet"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__active
msgid "Active"
msgstr "Aktiv"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_ids
msgid "Activities"
msgstr "Aktiviteter"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Aktivitet undtagelse markering"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_state
msgid "Activity State"
msgstr "Aktivitetstilstand"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_type_icon
msgid "Activity Type Icon"
msgstr "Aktivitetstype ikon"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_form
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_search
msgid "Archived"
msgstr "Arkiveret"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_attachment_count
msgid "Attachment Count"
msgstr "Antal vedhæftninger"

#. module: event_booth
#: model:ir.model.fields.selection,name:event_booth.selection__event_booth__state__available
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
msgid "Available"
msgstr "Til rådighed"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_event__event_booth_count_available
msgid "Available Booths"
msgstr "Stande til rådighed"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_booked_template
msgid "Booth"
msgstr "Stand"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_00_event_7
#: model:event.booth,name:event_booth.event_booth_0_event_0
msgid "Booth A1"
msgstr "Stand A1"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_01_event_7
#: model:event.booth,name:event_booth.event_booth_1_event_0
msgid "Booth A2"
msgstr "Stand A2"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_02_event_7
#: model:event.booth,name:event_booth.event_booth_2_event_0
msgid "Booth A3"
msgstr "Stand A3"

#. module: event_booth
#: model:mail.message.subtype,name:event_booth.mt_event_booth_booked
msgid "Booth Booked"
msgstr "Stand reserveret"

#. module: event_booth
#: model:ir.ui.menu,name:event_booth.menu_event_booth_category
msgid "Booth Categories"
msgstr "Standkategorier"

#. module: event_booth
#: model:ir.actions.act_window,name:event_booth.event_booth_category_action
#: model:ir.model.fields,field_description:event_booth.field_event_booth__booth_category_id
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__booth_category_id
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_form
msgid "Booth Category"
msgstr "Standkategori"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_event__event_booth_category_available_ids
msgid "Booth Category for which booths are still available. Used in frontend"
msgstr "Standkategori med ledige stande. Anvendes i frontend"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_form
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_search
msgid "Booth Type"
msgstr "Standtype"

#. module: event_booth
#: model_terms:ir.actions.act_window,help:event_booth.event_booth_category_action
msgid ""
"Booth categories are used to represent the different types of booths you "
"rent (Premium Booth, Table and Chairs, ...)"
msgstr ""
"Standkategorier anvendes til at repræsentere de forskellige standtyper, som "
"kan lejes (Premium-stand, borde og stole osv.)"

#. module: event_booth
#: model:ir.actions.act_window,name:event_booth.event_booth_action
#: model:ir.actions.act_window,name:event_booth.event_booth_action_from_event
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__booth_ids
#: model:ir.model.fields,field_description:event_booth.field_event_event__event_booth_ids
#: model:ir.model.fields,field_description:event_booth.field_event_type__event_type_booth_ids
#: model:ir.ui.menu,name:event_booth.menu_event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_form_from_event
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_tree_from_event
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_view_form
msgid "Booths"
msgstr "Stande"

#. module: event_booth
#: model_terms:ir.actions.act_window,help:event_booth.event_booth_action
#: model_terms:ir.actions.act_window,help:event_booth.event_booth_action_from_event
#: model_terms:ir.actions.act_window,help:event_booth.event_type_booth_action
msgid "Booths are the physical stands that you rent during your event."
msgstr ""
"Stande er fysiske boder, som kan lejes i forbindelse med et arrangement."

#. module: event_booth
#: model_terms:ir.actions.act_window,help:event_booth.event_booth_action
#: model_terms:ir.actions.act_window,help:event_booth.event_booth_action_from_event
msgid "Create a Booth"
msgstr "Opret en stand"

#. module: event_booth
#: model_terms:ir.actions.act_window,help:event_booth.event_booth_category_action
msgid "Create a Booth Category"
msgstr "Opret en standkategori"

#. module: event_booth
#: model_terms:ir.actions.act_window,help:event_booth.event_type_booth_action
msgid "Create a Type Booth"
msgstr "Opret en standtype"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__create_uid
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__create_uid
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__create_uid
msgid "Created by"
msgstr "Oprettet af"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__create_date
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__create_date
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__create_date
msgid "Created on"
msgstr "Oprettet den"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__description
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_form
msgid "Description"
msgstr "Beskrivelse"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__display_name
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__display_name
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__display_name
msgid "Display Name"
msgstr "Vis navn"

#. module: event_booth
#: model:ir.model,name:event_booth.model_event_event
#: model:ir.model.fields,field_description:event_booth.field_event_booth__event_id
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
msgid "Event"
msgstr "Arrangement"

#. module: event_booth
#: model:ir.model,name:event_booth.model_event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
msgid "Event Booth"
msgstr "Stand til arrangementer"

#. module: event_booth
#: model:ir.model,name:event_booth.model_event_booth_category
#: model:ir.model.fields,field_description:event_booth.field_event_event__event_booth_category_ids
msgid "Event Booth Category"
msgstr "Standkategori til arrangementer"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_event__event_booth_category_available_ids
msgid "Event Booth Category Available"
msgstr "Standkategori til arrangementer, der er til rådighed"

#. module: event_booth
#: model:ir.model,name:event_booth.model_event_type_booth
msgid "Event Booth Template"
msgstr "Skabelon til stand"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__event_type_id
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__event_type_id
msgid "Event Category"
msgstr "Arrangementskategori"

#. module: event_booth
#: model:ir.model,name:event_booth.model_event_type
msgid "Event Template"
msgstr "Begivenhed Skabelon"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_form_from_type
msgid "Event Type Booth"
msgstr "Arrangementstypestand"

#. module: event_booth
#: model:ir.actions.act_window,name:event_booth.event_type_booth_action
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_search
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_tree_from_type
msgid "Event Type Booths"
msgstr "Arrangementstypestande"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_graph
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_pivot
msgid "Event booth"
msgstr "Stand til arrangementer"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_follower_ids
msgid "Followers"
msgstr "Følgere"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_partner_ids
msgid "Followers (Partners)"
msgstr "Følgere (partnere)"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Skrifttype awesome icon f.eks. fa-opgaver"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_20_event_7
msgid "Gold Booth 1"
msgstr "Guldstand 1"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_21_event_7
msgid "Gold Booth 2"
msgstr "Guldstand 2"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_22_event_7
msgid "Gold Booth 3"
msgstr "Guldstand 3"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_search
msgid "Group By"
msgstr "Sortér efter"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__has_message
msgid "Has Message"
msgstr "Har besked"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__id
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__id
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__id
msgid "ID"
msgstr "ID"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikon for uventet aktivitet."

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Hvis afkrydset, kræver nye beskeder din opmærksomhed "

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__message_has_error
#: model:ir.model.fields,help:event_booth.field_event_booth__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Hvis afkrydset har nogle beskeder en leveringsfejl"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__image_1920
msgid "Image"
msgstr "Billede"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__image_1024
msgid "Image 1024"
msgstr "Billede 1024"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__image_128
msgid "Image 128"
msgstr "Billede 128"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__image_256
msgid "Image 256"
msgstr "Billede 256"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__image_512
msgid "Image 512"
msgstr "Billede 512"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__is_available
msgid "Is Available"
msgstr "Er Tilgængelig"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_is_follower
msgid "Is Follower"
msgstr "Er følger"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__write_uid
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__write_uid
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__write_uid
msgid "Last Updated by"
msgstr "Sidst opdateret af"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__write_date
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__write_date
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__write_date
msgid "Last Updated on"
msgstr "Sidst opdateret den"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_has_error
msgid "Message Delivery error"
msgstr "Besked ved leveringsfejl"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_ids
msgid "Messages"
msgstr "Beskeder"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Deadline på mine aktiviteter "

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__name
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__name
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__name
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_form_from_event
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_form_from_type
msgid "Name"
msgstr "Navn"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Næste aktivitet for kalenderarrangement"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Deadline for næste aktivitet"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_summary
msgid "Next Activity Summary"
msgstr "Oversigt over næste aktivitet"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_type_id
msgid "Next Activity Type"
msgstr "Næste aktivitetstype"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_needaction_counter
msgid "Number of Actions"
msgstr "Antal handlinger"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_has_error_counter
msgid "Number of errors"
msgstr "Antal fejl"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Antal meddelelser der kræver handling"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Antal beskeder med leveringsfejl"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_10_event_7
msgid "OpenWood Demonstrator 1"
msgstr "OpenWood-demonstrator 1"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_11_event_7
msgid "OpenWood Demonstrator 2"
msgstr "OpenWood-demonstrator 2"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_12_event_7
msgid "OpenWood Demonstrator 3"
msgstr "OpenWood-demonstrator 3"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_form_from_event
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_form_quick_create
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_form_from_type
msgid "Pick a Booth Category..."
msgstr "Vælg en standkategori ..."

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_form_from_event
msgid "Pick a Renter..."
msgstr "Vælg en lejer ..."

#. module: event_booth
#: model:event.booth.category,name:event_booth.event_booth_category_premium
msgid "Premium Booth"
msgstr "Premium-stand"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_3_event_0
msgid "Premium Booth A4"
msgstr "Premium-stand A4"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_2_event_2
#: model:event.booth,name:event_booth.event_booth_2_event_3
#: model:event.type.booth,name:event_booth.event_type_booth_demo_exhibition_2
msgid "Premium Showbooth 1"
msgstr "Premium-udstillingsstand 1"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_3_event_2
#: model:event.booth,name:event_booth.event_booth_3_event_3
#: model:event.type.booth,name:event_booth.event_type_booth_demo_exhibition_3
msgid "Premium Showbooth 2"
msgstr "Premium-udstillingsstand 2"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__rating_ids
msgid "Ratings"
msgstr "Bedømmelser"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__partner_id
msgid "Renter"
msgstr "Lejer"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__contact_email
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
msgid "Renter Email"
msgstr "Lejers e-mail"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__contact_name
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
msgid "Renter Name"
msgstr "Lejers navn"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__contact_phone
msgid "Renter Phone"
msgstr "Lejers telefonnummer"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_user_id
msgid "Responsible User"
msgstr "Ansvarlig bruger"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS leveringsfejl"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__sequence
msgid "Sequence"
msgstr "Sekvens"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_0_event_2
#: model:event.booth,name:event_booth.event_booth_0_event_3
#: model:event.type.booth,name:event_booth.event_type_booth_demo_exhibition_0
msgid "Showbooth 1"
msgstr "Udstillingsstand 1"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_1_event_2
#: model:event.booth,name:event_booth.event_booth_1_event_3
#: model:event.type.booth,name:event_booth.event_type_booth_demo_exhibition_1
msgid "Showbooth 2"
msgstr "Udstillingsstand 2"

#. module: event_booth
#: model:event.booth.category,name:event_booth.event_booth_category_standard
msgid "Standard Booth"
msgstr "Standardstand"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__state
msgid "Status"
msgstr "Status"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status baseret på aktiviteter\n"
"Forfaldne: Forfaldsdato er allerede overskredet\n"
"I dag: Aktivitetsdato er i dag\n"
"Planlagt: Fremtidige aktiviteter."

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_event__event_booth_count
msgid "Total Booths"
msgstr "Samlede antal stande"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type af undtagelsesaktivitet registreret "

#. module: event_booth
#: model:ir.model.fields.selection,name:event_booth.selection__event_booth__state__unavailable
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
msgid "Unavailable"
msgstr "Ikke ledig"

#. module: event_booth
#: model:event.booth.category,name:event_booth.event_booth_category_vip
msgid "VIP Booth"
msgstr "VIP-stand"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_4_event_0
msgid "VIP Booth A5"
msgstr "VIP-stand A5"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__website_message_ids
msgid "Website Messages"
msgstr "Beskeder fra hjemmesiden"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__website_message_ids
msgid "Website communication history"
msgstr "Hjemmesidens kommunikations historik"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_form
msgid "e.g. \"Those stands will be place near the entrance and...\""
msgstr "f.eks. \"De stande vil være placeret tæt ved indgangen og...\""

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_form_from_event
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_form_quick_create
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_form_from_type
msgid "e.g. First Booth Alley 1"
msgstr "f.eks. første standrække 1"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_form
msgid "e.g. Premium Booth"
msgstr "f.eks. Premium-stand"
