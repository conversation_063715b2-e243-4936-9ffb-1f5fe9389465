# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_fleet
# 
# Translators:
# emre <PERSON>tem, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# Halil, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# Cengiz Cokay Global Nonwoven, 2024
# Martin <PERSON>gaux, 2024
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>_<PERSON>do<PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-23 18:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_log_contract_view_form_inherit_hr
msgid "<span class=\"o_stat_text\">Employee</span>"
msgstr "<span class=\"o_stat_text\">Çalışan</span>"

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_view_form_inherit_hr
msgid ""
"<span class=\"o_stat_value\">1</span>\n"
"                        <span class=\"o_stat_text\">Employee</span>"
msgstr ""
"<span class=\"o_stat_value\">1</span>\n"
"                        <span class=\"o_stat_text\">Personel</span>"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_mail_activity_plan_template
msgid "Activity plan template"
msgstr "Aktivite planı şablonu"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_mail_activity_plan_template__responsible_type
msgid "Assignment"
msgstr "Atama"

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_assignation_log_employee_view_list
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_assignation_log_view_list
msgid "Attachments"
msgstr "Ekler"

#. module: hr_fleet
#. odoo-python
#: code:addons/hr_fleet/models/employee.py:0
msgid "Cannot remove address from employees with linked cars."
msgstr "Bağlantılı arabaları olan çalışanlardan adres kaldırılamaz."

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee__employee_cars_count
#: model:ir.model.fields,field_description:hr_fleet.field_res_users__employee_cars_count
msgid "Cars"
msgstr "Arabalar"

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.hr_departure_wizard_view_form
msgid "Company Car"
msgstr "Şirket Arabası"

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_assignation_log_employee_view_list
msgid "Current Driver"
msgstr "Mevcut Sürücü"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "Ayrılma Sihirbazı"

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_log_contract_view_form_inherit_hr
msgid "Driver"
msgstr "Sürücü"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle__driver_employee_id
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle_assignation_log__driver_employee_id
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle_log_contract__purchaser_employee_id
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle_log_services__purchaser_employee_id
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle_odometer__driver_employee_id
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_log_services_view_form_inherit_hr
msgid "Driver (Employee)"
msgstr "Sürücü (Çalışan)"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_fleet_vehicle_assignation_log
msgid "Drivers history on a vehicle"
msgstr "Bir araçtaki sürücü geçmişi"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_hr_employee
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_log_contract_view_search_inherit_hr
msgid "Employee"
msgstr "Çalışan"

#. module: hr_fleet
#. odoo-python
#: code:addons/hr_fleet/models/mail_activity_plan_template.py:0
msgid "Employee %s is not linked to a vehicle."
msgstr "%s çalışanına bağlı herhangi bir araç yoktur."

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle__driver_employee_name
msgid "Employee Name"
msgstr "Çalışan Adı"

#. module: hr_fleet
#: model:ir.model.fields.selection,name:hr_fleet.selection__mail_activity_plan_template__responsible_type__fleet_manager
msgid "Fleet Manager"
msgstr "Filo Yöneticisi"

#. module: hr_fleet
#. odoo-python
#: code:addons/hr_fleet/models/mail_activity_plan_template.py:0
msgid "Fleet Manager is limited to Employee plans."
msgstr "Filo Yöneticisi, Çalışan planlarıyla sınırlıdır."

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.view_employee_form
msgid "Fleet Mobility Card"
msgstr "Filo Mobilite Kartı"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle__future_driver_employee_id
msgid "Future Driver (Employee)"
msgstr "Gelecekteki Sürücü (Çalışan)"

#. module: hr_fleet
#. odoo-python
#: code:addons/hr_fleet/models/employee.py:0
msgid "History Employee Cars"
msgstr "Geçmiş Çalışan Araçları"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee__license_plate
msgid "License Plate"
msgstr "Plaka"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle__mobility_card
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee__mobility_card
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee_public__mobility_card
msgid "Mobility Card"
msgstr "Hareketlilik Kartı"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle_assignation_log__attachment_number
msgid "Number of Attachments"
msgstr "Eklentilerin Adedi"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_fleet_vehicle_odometer
msgid "Odometer log for a vehicle"
msgstr "Bir aracın kilometre sayacı kaydı"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_hr_employee_public
msgid "Public Employee"
msgstr "Herkese Açık Personel"

#. module: hr_fleet
#. odoo-python
#: code:addons/hr_fleet/models/fleet_vehicle.py:0
#: code:addons/hr_fleet/models/fleet_vehicle_log_contract.py:0
msgid "Related Employee"
msgstr "İlişkili personel"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_hr_departure_wizard__release_campany_car
msgid "Release Company Car"
msgstr "Şirket Arabasını Serbest Bırak"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_fleet_vehicle_log_services
msgid "Services for vehicles"
msgstr "Araçlar için Servisler"

#. module: hr_fleet
#. odoo-python
#: code:addons/hr_fleet/models/mail_activity_plan_template.py:0
msgid "The vehicle of employee %(employee)s is not linked to a fleet manager."
msgstr "%(employee)s çalışanının aracı bir filo yöneticisine bağlı değildir."

#. module: hr_fleet
#. odoo-javascript
#: code:addons/hr_fleet/static/src/views/hr_fleet_kanban/hr_fleet_kanban_controller.xml:0
msgid "Upload"
msgstr "Yükle"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_res_users
msgid "User"
msgstr "Kullanıcı"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_fleet_vehicle
msgid "Vehicle"
msgstr "Araç"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_fleet_vehicle_log_contract
msgid "Vehicle Contract"
msgstr "Araç Sözleşmesi"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee__car_ids
msgid "Vehicles (private)"
msgstr "Araçlar (özel)"
