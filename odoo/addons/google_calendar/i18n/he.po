# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* google_calendar
# 
# Translators:
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON>, 2024
# NoaFarkash, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# ya<PERSON> terner, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-27 18:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: ZVI BLONDER <<EMAIL>>, 2024\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
msgid "%(id)s and %(length)s following"
msgstr "%(id)s ולאחר מכן %(length)s"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid "%(reminder_type)s - %(duration)s Days"
msgstr "%(reminder_type)s-%(duration)s ימים"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid "%(reminder_type)s - %(duration)s Hours"
msgstr "%(reminder_type)s - %(duration)s שעות"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid "%(reminder_type)s - %(duration)s Minutes"
msgstr "%(reminder_type)s - %(duration)s דקות"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid "(No title)"
msgstr "(ללא כותרת)"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__active
#: model:ir.model.fields,field_description:google_calendar.field_calendar_recurrence__active
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_sync__active
msgid "Active"
msgstr "פעיל"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
msgid ""
"An administrator needs to configure Google Synchronization before you can "
"use it!"
msgstr "מנהל מערכת צריך להגדיר את תצורת הסנכרון של גוגל לפני שתוכל להשתמש בו!"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/res_users_settings.py:0
msgid ""
"An error occurred while generating the token. Your authorization code may be"
" invalid or has already expired [%s]. You should check your Client ID and "
"secret on the Google APIs plateform or try to stop and restart your calendar"
" synchronization."
msgstr ""

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_attendee
msgid "Calendar Attendee Information"
msgstr "מידע על משתתפי לוח השנה"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_event
msgid "Calendar Event"
msgstr "אירוע לוח שנה"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_cal_id
#: model:ir.model.fields,field_description:google_calendar.field_res_users_settings__google_calendar_cal_id
msgid "Calendar ID"
msgstr "מזהה לוח שנה"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.google_calendar_reset_account_view_form
msgid "Cancel"
msgstr "בטל"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "Client ID"
msgstr "מזהה לקוח"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "Client Secret"
msgstr "סוד לקוח"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings__cal_client_id
msgid "Client_id"
msgstr "Client_id"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings__cal_client_secret
msgid "Client_key"
msgstr "Client_key"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_res_config_settings
msgid "Config Settings"
msgstr "הגדרות תצורה"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
msgid "Configuration"
msgstr "תצורה"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
msgid "Configure"
msgstr "הגדר"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.google_calendar_reset_account_view_form
msgid "Confirm"
msgstr "אשר"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__delete_odoo
msgid "Delete from Odoo"
msgstr "מחק מהמערכת"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__delete_both
msgid "Delete from both"
msgstr "מחק משניהם"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__delete_google
msgid "Delete from the current Google Calendar account"
msgstr "מחק מהיומן של חשבון Google הנוכחי"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
msgid "Discard"
msgstr "בטל"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid "Email"
msgstr "דוא\"ל"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_recurrence
msgid "Event Recurrence Rule"
msgstr "כלל אירוע חוזר"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.xml:0
msgid "Google"
msgstr "גוגל"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_users_form
msgid "Google Calendar"
msgstr "לוח שנה של גוגל"

#. module: google_calendar
#: model:ir.actions.act_window,name:google_calendar.google_calendar_reset_account_action
#: model:ir.model,name:google_calendar.model_google_calendar_account_reset
msgid "Google Calendar Account Reset"
msgstr "אפס את היומן של חשבון Google"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__google_id
msgid "Google Calendar Event Id"
msgstr "מזהה אירוע לוח השנה של גוגל"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_recurrence__google_id
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_sync__google_id
msgid "Google Calendar Id"
msgstr "מזהה היומן של Google"

#. module: google_calendar
#: model:ir.actions.server,name:google_calendar.ir_cron_sync_all_cals_ir_actions_server
msgid "Google Calendar: synchronization"
msgstr "לוח שנה גוגל: סנכרון"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__calendar_event__videocall_source__google_meet
msgid "Google Meet"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings__cal_sync_paused
msgid "Google Synchronization Paused"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_synchronization_stopped
#: model:ir.model.fields,field_description:google_calendar.field_res_users_settings__google_synchronization_stopped
msgid "Google Synchronization stopped"
msgstr "סנכרון עם גוגל הופסק"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
msgid "Google gave the following explanation: %s"
msgstr "גוגל נתנו את ההסבר הבא: %s"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__guests_readonly
msgid "Guests Event Modification Permission"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__id
msgid "ID"
msgstr "מזהה"

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_calendar_event__active
msgid ""
"If the active field is set to false, it will allow you to hide the event "
"alarm information without removing it."
msgstr ""
"אם השדה הפעיל מוגדר כ- לא נכון, הוא יאפשר להסתיר את מידע אזעקת האירוע מבלי "
"להסיר אותו."

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_res_config_settings__cal_sync_paused
msgid "Indicates if synchronization with Google Calendar is paused or not."
msgstr ""

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
msgid "It will not be synced as long at it is not updated."
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_res_users__google_calendar_cal_id
#: model:ir.model.fields,help:google_calendar.field_res_users_settings__google_calendar_cal_id
msgid ""
"Last Calendar ID who has been synchronized. If it is changed, we remove all "
"links between GoogleID and Odoo Google Internal ID"
msgstr ""
"מזהה לוח השנה האחרון שסונכרן. אם זה משתנה, אנו מסירים את כל הקישורים בין "
"מזהה גוגל למזהה הפנימי של גוגל Odoo"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__dont_delete
msgid "Leave them untouched"
msgstr "השאר ללא שינוי"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__need_sync
#: model:ir.model.fields,field_description:google_calendar.field_calendar_recurrence__need_sync
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_sync__need_sync
msgid "Need Sync"
msgstr "יש צורך בסינכרון"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_sync_token
#: model:ir.model.fields,field_description:google_calendar.field_res_users_settings__google_calendar_sync_token
msgid "Next Sync Token"
msgstr "האסימון לסנכרון הבא:"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__sync_policy
msgid "Next Synchronization"
msgstr "הסנכרון הבא"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid "Notification"
msgstr "התראה"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "Pause Synchronization"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_rtoken
#: model:ir.model.fields,field_description:google_calendar.field_res_users_settings__google_calendar_rtoken
msgid "Refresh Token"
msgstr "רענן אסימון"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_users_form
msgid "Reset Account"
msgstr "אפס חשבון"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.google_calendar_reset_account_view_form
msgid "Reset Google Calendar Account"
msgstr "אפס את היומן של Google"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_google_calendar_sync
msgid "Synchronize a record with Google Calendar"
msgstr "סנכרן רשומה עם לוח השנה של Google"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__sync_policy__all
msgid "Synchronize all existing events"
msgstr "סנכרן את כל הארועים הקיימים"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__sync_policy__new
msgid "Synchronize only new events"
msgstr "סנכרן רק אירועים החדשים"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
msgid ""
"The Google Synchronization needs to be configured before you can use it, do "
"you want to do it now?"
msgstr ""
"צריך להגדיר את הסנכרון של גוגל לפני שתוכל להשתמש בו, האם אתה רוצה לעשות זאת "
"עכשיו?"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid ""
"The following event can only be updated by the organizer according to the "
"event permissions set on Google Calendar."
msgstr ""

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
msgid "The following event could not be synced with Google Calendar."
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_google_calendar_account_reset__delete_policy
msgid "This will only affect events for which the user is the owner"
msgstr "ישפיע על אירועים של המשתמש"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_token_validity
#: model:ir.model.fields,field_description:google_calendar.field_res_users_settings__google_calendar_token_validity
msgid "Token Validity"
msgstr "תוקף אסימון"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_res_users
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__user_id
msgid "User"
msgstr "משתמש"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_res_users_settings
msgid "User Settings"
msgstr "הגדרות משתמש"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_token
#: model:ir.model.fields,field_description:google_calendar.field_res_users_settings__google_calendar_token
msgid "User token"
msgstr "אסימון משתמש"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__delete_policy
msgid "User's Existing Events"
msgstr "אירועים של משתמש"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__videocall_source
msgid "Videocall Source"
msgstr "מקור שיחת וידאו"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
msgid "undefined time"
msgstr "זמן לא מוגדר"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
msgid ""
"you don't seem to have permission to modify this event on Google Calendar"
msgstr "נראה שאין לך הרשאה לשנות את האירוע הזה ביומן Google"
