# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* google_calendar
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-27 18:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
msgid "%(id)s and %(length)s following"
msgstr "%(id)s și %(length)s următoare"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid "%(reminder_type)s - %(duration)s Days"
msgstr "%(reminder_type)s - %(duration)s Zile"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid "%(reminder_type)s - %(duration)s Hours"
msgstr "%(reminder_type)s - %(duration)s Ore"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid "%(reminder_type)s - %(duration)s Minutes"
msgstr "%(reminder_type)s - %(duration)s Minute"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid "(No title)"
msgstr "(Fără Titlu)"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__active
#: model:ir.model.fields,field_description:google_calendar.field_calendar_recurrence__active
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_sync__active
msgid "Active"
msgstr "Activ"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
msgid ""
"An administrator needs to configure Google Synchronization before you can "
"use it!"
msgstr ""
"Un administrator trebuie să configureze Sincronizarea Google înainte de a "
"putea să o folosești!"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/res_users_settings.py:0
msgid ""
"An error occurred while generating the token. Your authorization code may be"
" invalid or has already expired [%s]. You should check your Client ID and "
"secret on the Google APIs plateform or try to stop and restart your calendar"
" synchronization."
msgstr ""

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_attendee
msgid "Calendar Attendee Information"
msgstr "Informații despre participanții din calendar"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_event
msgid "Calendar Event"
msgstr "Eveniment Calendar"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_cal_id
#: model:ir.model.fields,field_description:google_calendar.field_res_users_settings__google_calendar_cal_id
msgid "Calendar ID"
msgstr "Calendar ID"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.google_calendar_reset_account_view_form
msgid "Cancel"
msgstr "Anulează"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "Client ID"
msgstr "ID Client"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "Client Secret"
msgstr "Secret Client"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings__cal_client_id
msgid "Client_id"
msgstr "Client_id"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings__cal_client_secret
msgid "Client_key"
msgstr "Client_key"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_res_config_settings
msgid "Config Settings"
msgstr "Setări de configurare"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
msgid "Configuration"
msgstr "Configurare"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
msgid "Configure"
msgstr "Configurare"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.google_calendar_reset_account_view_form
msgid "Confirm"
msgstr "Confirmă"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__create_date
msgid "Created on"
msgstr "Creat pe"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__delete_odoo
msgid "Delete from Odoo"
msgstr "Șterge din Odoo"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__delete_both
msgid "Delete from both"
msgstr "Șterge din amândouă"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__delete_google
msgid "Delete from the current Google Calendar account"
msgstr "Ștergeți din contul curent Google Calendar"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
msgid "Discard"
msgstr "Abandonează"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid "Email"
msgstr "E-mail"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_recurrence
msgid "Event Recurrence Rule"
msgstr "Regula de reapariție a evenimentelor"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.xml:0
msgid "Google"
msgstr "Google"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_users_form
msgid "Google Calendar"
msgstr "Calendar Google"

#. module: google_calendar
#: model:ir.actions.act_window,name:google_calendar.google_calendar_reset_account_action
#: model:ir.model,name:google_calendar.model_google_calendar_account_reset
msgid "Google Calendar Account Reset"
msgstr "Resetare cont Google Calendar"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__google_id
msgid "Google Calendar Event Id"
msgstr "ID Eveniment Google Calendar"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_recurrence__google_id
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_sync__google_id
msgid "Google Calendar Id"
msgstr "Id Google Calendar"

#. module: google_calendar
#: model:ir.actions.server,name:google_calendar.ir_cron_sync_all_cals_ir_actions_server
msgid "Google Calendar: synchronization"
msgstr "Google Calendar: sincronizare"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__calendar_event__videocall_source__google_meet
msgid "Google Meet"
msgstr "Google Meet"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings__cal_sync_paused
msgid "Google Synchronization Paused"
msgstr "Sincronizarea Google a fost întreruptă"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_synchronization_stopped
#: model:ir.model.fields,field_description:google_calendar.field_res_users_settings__google_synchronization_stopped
msgid "Google Synchronization stopped"
msgstr "Sincronizarea Google a fost oprită"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
msgid "Google gave the following explanation: %s"
msgstr "Google a dat următoarea explicație: %s"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__guests_readonly
msgid "Guests Event Modification Permission"
msgstr "Permisiune de modificare a evenimentului pentru invitați"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__id
msgid "ID"
msgstr "ID"

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_calendar_event__active
msgid ""
"If the active field is set to false, it will allow you to hide the event "
"alarm information without removing it."
msgstr ""
"Dacă câmpul nu este setat, vă permite să ascundeți evenimentul fără să-l "
"eliminați."

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_res_config_settings__cal_sync_paused
msgid "Indicates if synchronization with Google Calendar is paused or not."
msgstr "Indică dacă sincronizarea cu Google Calendar este suspendată sau nu."

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
msgid "It will not be synced as long at it is not updated."
msgstr "Acesta nu va fi sincronizat atâta timp cât nu este actualizat."

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_res_users__google_calendar_cal_id
#: model:ir.model.fields,help:google_calendar.field_res_users_settings__google_calendar_cal_id
msgid ""
"Last Calendar ID who has been synchronized. If it is changed, we remove all "
"links between GoogleID and Odoo Google Internal ID"
msgstr ""
"ID-ul ultimului calendar care a fost sincronizat. Dacă este modificat, "
"eliminăm toate legăturile dintre GoogleID și Odoo Google Internal ID"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__dont_delete
msgid "Leave them untouched"
msgstr "Lăsați-le neatinse"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__need_sync
#: model:ir.model.fields,field_description:google_calendar.field_calendar_recurrence__need_sync
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_sync__need_sync
msgid "Need Sync"
msgstr "Aveți nevoie de sincronizare"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_sync_token
#: model:ir.model.fields,field_description:google_calendar.field_res_users_settings__google_calendar_sync_token
msgid "Next Sync Token"
msgstr "Aveți nevoie de token de sincronizare"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__sync_policy
msgid "Next Synchronization"
msgstr "Sincronizarea urmatoare"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid "Notification"
msgstr "Notificare"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "Pause Synchronization"
msgstr "Suspendare sincronizare"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_rtoken
#: model:ir.model.fields,field_description:google_calendar.field_res_users_settings__google_calendar_rtoken
msgid "Refresh Token"
msgstr "Token de Actualizare"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_users_form
msgid "Reset Account"
msgstr "Resetare Cont"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.google_calendar_reset_account_view_form
msgid "Reset Google Calendar Account"
msgstr "Resetare Cont Google Calender"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_google_calendar_sync
msgid "Synchronize a record with Google Calendar"
msgstr "Sincronizați o înregistrare cu Google Calendar"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__sync_policy__all
msgid "Synchronize all existing events"
msgstr "Sincronizați toate evenimentele existente"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__sync_policy__new
msgid "Synchronize only new events"
msgstr "Sincronizați numai evenimente noi"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
msgid ""
"The Google Synchronization needs to be configured before you can use it, do "
"you want to do it now?"
msgstr ""
"Sincronizarea Google trebuie să fie configurată înainte de ao putea utiliza,"
" doriți să o faceți acum?"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid ""
"The following event can only be updated by the organizer according to the "
"event permissions set on Google Calendar."
msgstr ""
"Următorul eveniment poate fi actualizat numai de către organizator, în "
"conformitate cu permisiunile evenimentului stabilite în Google Calendar."

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
msgid "The following event could not be synced with Google Calendar."
msgstr "Următorul eveniment nu a putut fi sincronizat cu Google Calendar."

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_google_calendar_account_reset__delete_policy
msgid "This will only affect events for which the user is the owner"
msgstr ""
"Acest lucru va afecta doar evenimentele pentru care utilizatorul este "
"proprietar"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_token_validity
#: model:ir.model.fields,field_description:google_calendar.field_res_users_settings__google_calendar_token_validity
msgid "Token Validity"
msgstr "Validitate Token"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_res_users
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__user_id
msgid "User"
msgstr "Utilizator"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_res_users_settings
msgid "User Settings"
msgstr "Setări utilizator"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_token
#: model:ir.model.fields,field_description:google_calendar.field_res_users_settings__google_calendar_token
msgid "User token"
msgstr "Jeton Utilizator"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__delete_policy
msgid "User's Existing Events"
msgstr "Evenimente existente ale utilizatorului"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__videocall_source
msgid "Videocall Source"
msgstr "Sursă videocall"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
msgid "undefined time"
msgstr "timp nedefinit"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
msgid ""
"you don't seem to have permission to modify this event on Google Calendar"
msgstr ""
"se pare că nu aveți permisiunea de a modifica acest eveniment pe Google"
