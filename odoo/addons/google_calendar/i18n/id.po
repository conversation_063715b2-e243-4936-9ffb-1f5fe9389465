# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* google_calendar
# 
# Translators:
# Abe Manyo, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-27 18:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
msgid "%(id)s and %(length)s following"
msgstr "%(id)s dan %(length)s mengikuti"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid "%(reminder_type)s - %(duration)s Days"
msgstr "%(reminder_type)s - %(duration)s Hari"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid "%(reminder_type)s - %(duration)s Hours"
msgstr "%(reminder_type)s - %(duration)s Jam"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid "%(reminder_type)s - %(duration)s Minutes"
msgstr "%(reminder_type)s - %(duration)s Menit"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid "(No title)"
msgstr "(Tidak ada judul)"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__active
#: model:ir.model.fields,field_description:google_calendar.field_calendar_recurrence__active
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_sync__active
msgid "Active"
msgstr "Aktif"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
msgid ""
"An administrator needs to configure Google Synchronization before you can "
"use it!"
msgstr ""
"Administrator perlu mengkonfigurasi Sinkronisasi Google sebelum Anda dapat "
"menggunakannya!"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/res_users_settings.py:0
msgid ""
"An error occurred while generating the token. Your authorization code may be"
" invalid or has already expired [%s]. You should check your Client ID and "
"secret on the Google APIs plateform or try to stop and restart your calendar"
" synchronization."
msgstr ""
"Terjadi error saat membuat token. Kode otorisasi Anda mungkin tidak valid "
"atau sudah kadaluwarsa [%s]. Anda sebaiknya memeriksa ID Klien Anda dan "
"secret pada platform Google API atau coba untuk memberhentikan dan restart "
"sinkronisasi kalender Anda."

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_attendee
msgid "Calendar Attendee Information"
msgstr "Informasi Kalender Peserta"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_event
msgid "Calendar Event"
msgstr "Acara Kalender"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_cal_id
#: model:ir.model.fields,field_description:google_calendar.field_res_users_settings__google_calendar_cal_id
msgid "Calendar ID"
msgstr "ID kalender"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.google_calendar_reset_account_view_form
msgid "Cancel"
msgstr "Batal"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "Client ID"
msgstr "Client ID"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "Client Secret"
msgstr "Klien Rahasia"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings__cal_client_id
msgid "Client_id"
msgstr "Client_id"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings__cal_client_secret
msgid "Client_key"
msgstr "Client_key"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_res_config_settings
msgid "Config Settings"
msgstr "Pengaturan Konfigurasi"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
msgid "Configuration"
msgstr "Konfigurasi"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
msgid "Configure"
msgstr "Konfigurasi"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.google_calendar_reset_account_view_form
msgid "Confirm"
msgstr "Konfirmasi"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__create_uid
msgid "Created by"
msgstr "Dibuat oleh"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__create_date
msgid "Created on"
msgstr "Dibuat pada"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__delete_odoo
msgid "Delete from Odoo"
msgstr "Hapus dari Odoo"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__delete_both
msgid "Delete from both"
msgstr "Hapus dari keduanya"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__delete_google
msgid "Delete from the current Google Calendar account"
msgstr "Hapus dari akun Google Calendar"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
msgid "Discard"
msgstr "Buang"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid "Email"
msgstr "Email"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_recurrence
msgid "Event Recurrence Rule"
msgstr "Peraturan Acara Rutin"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.xml:0
msgid "Google"
msgstr "Google"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_users_form
msgid "Google Calendar"
msgstr "Google Calendar"

#. module: google_calendar
#: model:ir.actions.act_window,name:google_calendar.google_calendar_reset_account_action
#: model:ir.model,name:google_calendar.model_google_calendar_account_reset
msgid "Google Calendar Account Reset"
msgstr "Reset Akun Google Calendar"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__google_id
msgid "Google Calendar Event Id"
msgstr "Google Calendar Acara Id"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_recurrence__google_id
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_sync__google_id
msgid "Google Calendar Id"
msgstr "ID Google Calendar"

#. module: google_calendar
#: model:ir.actions.server,name:google_calendar.ir_cron_sync_all_cals_ir_actions_server
msgid "Google Calendar: synchronization"
msgstr "Google Calendar: sinkronisasi"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__calendar_event__videocall_source__google_meet
msgid "Google Meet"
msgstr "Google Meet"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings__cal_sync_paused
msgid "Google Synchronization Paused"
msgstr "Sinkronisasi Google Dipause"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_synchronization_stopped
#: model:ir.model.fields,field_description:google_calendar.field_res_users_settings__google_synchronization_stopped
msgid "Google Synchronization stopped"
msgstr "Sinkronisasi Google diberhentikan"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
msgid "Google gave the following explanation: %s"
msgstr "Google memberikan penjelasan berikut: %s"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__guests_readonly
msgid "Guests Event Modification Permission"
msgstr "Ijin Modifikasi Tamu Acara"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__id
msgid "ID"
msgstr "ID"

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_calendar_event__active
msgid ""
"If the active field is set to false, it will allow you to hide the event "
"alarm information without removing it."
msgstr ""
"Jika kolom aktif distel ke Salah, Anda dapat menyembunyikan informasi alarm "
"acara tanpa menghapusnya."

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_res_config_settings__cal_sync_paused
msgid "Indicates if synchronization with Google Calendar is paused or not."
msgstr ""
"Mengindikasikan bila sinkronisasi dengan Google Calendar dipause atau tidak."

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
msgid "It will not be synced as long at it is not updated."
msgstr "Tidak akan disinkronisasi selama belum diupdate."

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_res_users__google_calendar_cal_id
#: model:ir.model.fields,help:google_calendar.field_res_users_settings__google_calendar_cal_id
msgid ""
"Last Calendar ID who has been synchronized. If it is changed, we remove all "
"links between GoogleID and Odoo Google Internal ID"
msgstr ""
"ID Kalender terakhir yang telah disinkronkan. Jika berubah, kita menghapus "
"semua link antara GoogleID dan Odoo Google ID internal"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__write_uid
msgid "Last Updated by"
msgstr "Terakhir Diperbarui oleh"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__write_date
msgid "Last Updated on"
msgstr "Terakhir Diperbarui pada"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__dont_delete
msgid "Leave them untouched"
msgstr "Jangan sentuh mereka"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__need_sync
#: model:ir.model.fields,field_description:google_calendar.field_calendar_recurrence__need_sync
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_sync__need_sync
msgid "Need Sync"
msgstr "Butuh Sinkronisasi"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_sync_token
#: model:ir.model.fields,field_description:google_calendar.field_res_users_settings__google_calendar_sync_token
msgid "Next Sync Token"
msgstr "Token Sinkronisasi Berikutnya"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__sync_policy
msgid "Next Synchronization"
msgstr "Sinkronisasi Berikutnya"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid "Notification"
msgstr "Notifikasi"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "Pause Synchronization"
msgstr "Pause Sinkronisasi"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_rtoken
#: model:ir.model.fields,field_description:google_calendar.field_res_users_settings__google_calendar_rtoken
msgid "Refresh Token"
msgstr "Refresh Token"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_users_form
msgid "Reset Account"
msgstr "Reset Akun"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.google_calendar_reset_account_view_form
msgid "Reset Google Calendar Account"
msgstr "Reset Akun Google Calendar"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_google_calendar_sync
msgid "Synchronize a record with Google Calendar"
msgstr "Sinkronisasi record dengan Google Calendar"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__sync_policy__all
msgid "Synchronize all existing events"
msgstr "Sinkronisasi semua acara yang tersedia"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__sync_policy__new
msgid "Synchronize only new events"
msgstr "Sinkronisasi hanya acara baru"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
msgid ""
"The Google Synchronization needs to be configured before you can use it, do "
"you want to do it now?"
msgstr ""
"Google Sinkronisasi perlu dikonfigurasi sebelum Anda dapat menggunakannya, "
"apakah Anda ingin melakukannya sekarang?"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid ""
"The following event can only be updated by the organizer according to the "
"event permissions set on Google Calendar."
msgstr ""
"Acara berikut hanya dapat diupdate oleh organizer menurut izin acara yang "
"ditetapkan pada Google Calendar."

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
msgid "The following event could not be synced with Google Calendar."
msgstr "Acara berikut tidak dapat disinkronisasi dengan Google Calendar."

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_google_calendar_account_reset__delete_policy
msgid "This will only affect events for which the user is the owner"
msgstr "Ini hanya akan berdampak pada acara yang mana user merupakan pemilik"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_token_validity
#: model:ir.model.fields,field_description:google_calendar.field_res_users_settings__google_calendar_token_validity
msgid "Token Validity"
msgstr "Token Validitas"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_res_users
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__user_id
msgid "User"
msgstr "Pengguna"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_res_users_settings
msgid "User Settings"
msgstr "Pengaturan User"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_token
#: model:ir.model.fields,field_description:google_calendar.field_res_users_settings__google_calendar_token
msgid "User token"
msgstr "Token pengguna"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__delete_policy
msgid "User's Existing Events"
msgstr "Acara User yang Tersedia"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__videocall_source
msgid "Videocall Source"
msgstr "Sumber Videocall"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
msgid "undefined time"
msgstr "waktu belum didefinisikan"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
msgid ""
"you don't seem to have permission to modify this event on Google Calendar"
msgstr ""
"Anda sepertinya tidak memiliki izin untuk memodifikasi acara ini pada Google"
" Calendar"
