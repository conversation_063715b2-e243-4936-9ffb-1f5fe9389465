# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* google_calendar
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-27 18:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
msgid "%(id)s and %(length)s following"
msgstr "%(id)s та %(length)s наступні"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid "%(reminder_type)s - %(duration)s Days"
msgstr "%(reminder_type)s - %(duration)s днів"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid "%(reminder_type)s - %(duration)s Hours"
msgstr "%(reminder_type)s - %(duration)s годин"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid "%(reminder_type)s - %(duration)s Minutes"
msgstr "%(reminder_type)s - %(duration)s хвилин"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid "(No title)"
msgstr "(Немає заголовка)"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__active
#: model:ir.model.fields,field_description:google_calendar.field_calendar_recurrence__active
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_sync__active
msgid "Active"
msgstr "Активно"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
msgid ""
"An administrator needs to configure Google Synchronization before you can "
"use it!"
msgstr ""
"Адміністратор повинен налаштувати Синхронізацію Google, перш ніж "
"користуватися нею!"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/res_users_settings.py:0
msgid ""
"An error occurred while generating the token. Your authorization code may be"
" invalid or has already expired [%s]. You should check your Client ID and "
"secret on the Google APIs plateform or try to stop and restart your calendar"
" synchronization."
msgstr ""

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_attendee
msgid "Calendar Attendee Information"
msgstr "Інформація календаря учасника"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_event
msgid "Calendar Event"
msgstr "Календар подій"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_cal_id
#: model:ir.model.fields,field_description:google_calendar.field_res_users_settings__google_calendar_cal_id
msgid "Calendar ID"
msgstr "ID календаря"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.google_calendar_reset_account_view_form
msgid "Cancel"
msgstr "Скасувати"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "Client ID"
msgstr "ID клієнта"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "Client Secret"
msgstr "Пароль клієнта"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings__cal_client_id
msgid "Client_id"
msgstr "ID клієнта"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings__cal_client_secret
msgid "Client_key"
msgstr "Ключ клієнта"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_res_config_settings
msgid "Config Settings"
msgstr "Налаштування"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
msgid "Configuration"
msgstr "Налаштування"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
msgid "Configure"
msgstr "Налаштувати"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.google_calendar_reset_account_view_form
msgid "Confirm"
msgstr "Підтвердити"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__create_uid
msgid "Created by"
msgstr "Створив"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__create_date
msgid "Created on"
msgstr "Створено"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__delete_odoo
msgid "Delete from Odoo"
msgstr "Видалити з Odoo"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__delete_both
msgid "Delete from both"
msgstr "Видалити з обох"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__delete_google
msgid "Delete from the current Google Calendar account"
msgstr "Видалити з поточного облікового запису Google Календаря"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
msgid "Discard"
msgstr "Відмінити"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__display_name
msgid "Display Name"
msgstr "Назва для відображення"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid "Email"
msgstr "Ел. пошта"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_recurrence
msgid "Event Recurrence Rule"
msgstr "Правило повторюваної події"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.xml:0
msgid "Google"
msgstr "Google"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_users_form
msgid "Google Calendar"
msgstr "Google Календар"

#. module: google_calendar
#: model:ir.actions.act_window,name:google_calendar.google_calendar_reset_account_action
#: model:ir.model,name:google_calendar.model_google_calendar_account_reset
msgid "Google Calendar Account Reset"
msgstr "Скинути обліковий запис Google Календаря"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__google_id
msgid "Google Calendar Event Id"
msgstr "ID події Google календаря "

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_recurrence__google_id
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_sync__google_id
msgid "Google Calendar Id"
msgstr "Id Google Календаря"

#. module: google_calendar
#: model:ir.actions.server,name:google_calendar.ir_cron_sync_all_cals_ir_actions_server
msgid "Google Calendar: synchronization"
msgstr "Google Calendar: синхронізація"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__calendar_event__videocall_source__google_meet
msgid "Google Meet"
msgstr "Google Meet"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings__cal_sync_paused
msgid "Google Synchronization Paused"
msgstr "Google синхронізація на паузі"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_synchronization_stopped
#: model:ir.model.fields,field_description:google_calendar.field_res_users_settings__google_synchronization_stopped
msgid "Google Synchronization stopped"
msgstr "Google синхронізація призупинена"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
msgid "Google gave the following explanation: %s"
msgstr "Google дав таке пояснення: %s"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__guests_readonly
msgid "Guests Event Modification Permission"
msgstr "Дозвіл на зміну події гостей"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__id
msgid "ID"
msgstr "ID"

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_calendar_event__active
msgid ""
"If the active field is set to false, it will allow you to hide the event "
"alarm information without removing it."
msgstr ""
"Якщо активне поле налаштовано невірно, ви зможете приховати інформацію про "
"сповіщення події, не видаляючи її."

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_res_config_settings__cal_sync_paused
msgid "Indicates if synchronization with Google Calendar is paused or not."
msgstr "Вказує, чи призупинено синхронізацію з Календарем Google."

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
msgid "It will not be synced as long at it is not updated."
msgstr "Він не буде синхронізований, доки не буде оновлено."

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_res_users__google_calendar_cal_id
#: model:ir.model.fields,help:google_calendar.field_res_users_settings__google_calendar_cal_id
msgid ""
"Last Calendar ID who has been synchronized. If it is changed, we remove all "
"links between GoogleID and Odoo Google Internal ID"
msgstr ""
"ID останнього календаря, який був синхронізований. Якщо він буде змінений, "
"ми видалимо всі посилання між GoogleID і Odoo Google Internal ID"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__write_uid
msgid "Last Updated by"
msgstr "Востаннє оновив"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__write_date
msgid "Last Updated on"
msgstr "Останнє оновлення"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__dont_delete
msgid "Leave them untouched"
msgstr "Залиште його незайманим"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__need_sync
#: model:ir.model.fields,field_description:google_calendar.field_calendar_recurrence__need_sync
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_sync__need_sync
msgid "Need Sync"
msgstr "Необхідна синхронізація"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_sync_token
#: model:ir.model.fields,field_description:google_calendar.field_res_users_settings__google_calendar_sync_token
msgid "Next Sync Token"
msgstr "Токен наступної синхронізації"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__sync_policy
msgid "Next Synchronization"
msgstr "Наступна синхронізація"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid "Notification"
msgstr "Сповіщення"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "Pause Synchronization"
msgstr "Поставити на паузу синзронізацію"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_rtoken
#: model:ir.model.fields,field_description:google_calendar.field_res_users_settings__google_calendar_rtoken
msgid "Refresh Token"
msgstr "Оновити токен"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_users_form
msgid "Reset Account"
msgstr "Скинути обліковий запис"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.google_calendar_reset_account_view_form
msgid "Reset Google Calendar Account"
msgstr "Скинути обліковий запис Google Календаря"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_google_calendar_sync
msgid "Synchronize a record with Google Calendar"
msgstr "Синхронізувати запис з Google Календарем"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__sync_policy__all
msgid "Synchronize all existing events"
msgstr "Синхронізувати всі існуючі події"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__sync_policy__new
msgid "Synchronize only new events"
msgstr "Синхронізувати лише нові події"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
msgid ""
"The Google Synchronization needs to be configured before you can use it, do "
"you want to do it now?"
msgstr ""
"Синхронізацію Google потрібно налаштувати, перш ніж використовувати її, чи "
"хочете ви це зробити зараз?"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid ""
"The following event can only be updated by the organizer according to the "
"event permissions set on Google Calendar."
msgstr ""
"Наступну подію може оновити лише організатор відповідно до дозволів події, "
"установлених у Календарі Google."

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
msgid "The following event could not be synced with Google Calendar."
msgstr "Наступну подію не вдалося синхронізувати з Календарем Google."

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_google_calendar_account_reset__delete_policy
msgid "This will only affect events for which the user is the owner"
msgstr "Це вплине лише на ті події, де користувач є власником"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_token_validity
#: model:ir.model.fields,field_description:google_calendar.field_res_users_settings__google_calendar_token_validity
msgid "Token Validity"
msgstr "Термін дії токена"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_res_users
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__user_id
msgid "User"
msgstr "Користувач"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_res_users_settings
msgid "User Settings"
msgstr "Налаштування користувача"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_token
#: model:ir.model.fields,field_description:google_calendar.field_res_users_settings__google_calendar_token
msgid "User token"
msgstr "Токен користувача"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__delete_policy
msgid "User's Existing Events"
msgstr "Існуючі події користувача"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__videocall_source
msgid "Videocall Source"
msgstr "Джерело відеодзвінка"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
msgid "undefined time"
msgstr "невизначений час"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
msgid ""
"you don't seem to have permission to modify this event on Google Calendar"
msgstr "схоже, у вас немає дозволу змінювати цю подію в Google календарі"
