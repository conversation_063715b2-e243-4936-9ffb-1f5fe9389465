# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:03+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: delivery
#. odoo-python
#: code:addons/delivery/wizard/choose_delivery_carrier.py:0
msgid "%(carrier)s Error"
msgstr "Error de %(carrier)s"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/sale_order.py:0
msgid ""
"%s\n"
"Free Shipping"
msgstr ""
"%s\n"
"Envío gratis"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
msgid "%s (copy)"
msgstr "%s (copia)"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_carrier_view_form
msgid "<i class=\"oi oi-arrow-right me-1\"/>Get rate"
msgstr "<i class=\"oi oi-arrow-right me-1\"/>Obtener tarifa"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
msgid ""
"<p class=\"o_view_nocontent\">\n"
"                    Buy Odoo Enterprise now to get more providers.\n"
"                </p>"
msgstr ""
"<p class=\"o_view_nocontent\">\n"
"                    Adquiere Odoo Enterprise ahora para obtener más proveedores.\n"
"                         </p>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid ""
"<span class=\"o_stat_text o_warning_text fw-bold\">Test</span>\n"
"                                <span class=\"o_stat_text\">Environment</span>"
msgstr ""
"<span class=\"o_stat_text o_warning_text fw-bold\">Entorno de</span>\n"
"                                <span class=\"o_stat_text\">prueba</span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "<span class=\"o_stat_text text-danger\">No debug</span>"
msgstr "<span class=\"o_stat_text text-danger\">Sin depuración</span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "<span class=\"text-success\">Debug requests</span>"
msgstr "<span class=\"text-success\">Solicitudes de depuración</span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid ""
"<span class=\"text-success\">Production</span>\n"
"                                <span class=\"o_stat_text\">Environment</span>"
msgstr ""
"<span class=\"text-success\">Entorno de</span>\n"
"                                <span class=\"o_stat_text\">producción</span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.delivery_report_saleorder_document
msgid "<strong>Shipping Description</strong>"
msgstr "<strong>Descripción de envío</strong>"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__carrier_description
msgid ""
"A description of the delivery method that you want to communicate to your "
"customers on the Sales Order and sales confirmation email.E.g. instructions "
"for customers to follow."
msgstr ""
"Una descripción del método de envío que desea comunicar a sus clientes en el"
" pedido de venta y en el correo electrónico de confirmación de venta. Por "
"ejemplo, instrucciones que el cliente debe seguir."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__integration_level
msgid "Action while validating Delivery Orders"
msgstr "Acción al validar órdenes de entrega"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__active
msgid "Active"
msgstr "Activo"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_carrier_view_form
msgid "Add"
msgstr "Añadir"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/sale_order.py:0
#: code:addons/delivery/wizard/choose_delivery_carrier.py:0
msgid "Add a shipping method"
msgstr "Añadir un método de envío"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_order_form_with_carrier
msgid "Add shipping"
msgstr "Añadir envío"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Additional margin"
msgstr "Margen adicional"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__amount
msgid "Amount"
msgstr "Importe"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__amount
msgid ""
"Amount of the order to benefit from a free shipping, expressed in the "
"company currency"
msgstr ""
"Importe de la orden para recibir envío gratis, expresado en la moneda de la "
"empresa"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Archived"
msgstr "Archivado"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Availability"
msgstr "Disponibilidad"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__available_carrier_ids
msgid "Available Carriers"
msgstr "Transportistas disponibles"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__delivery_type__base_on_rule
msgid "Based on Rules"
msgstr "Basado en reglas"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__can_generate_return
msgid "Can Generate Return"
msgstr "Puede generar una devolución"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__carrier_id
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_tree
msgid "Carrier"
msgstr "Transportista"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
msgid ""
"Carrier %s cannot have the same tag in both Must Have Tags and Excluded "
"Tags."
msgstr ""
"El transportista %s no puede tener la misma etiqueta en “Etiquetas que debe "
"tener” y “Etiquetas excluidas”."

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__carrier_description
msgid "Carrier Description"
msgstr "Descripción del transportista"

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
msgid "Choose a pick-up point"
msgstr "Elija un punto de recogida"

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
#: code:addons/delivery/static/src/js/location_selector/map_container/map_container.js:0
msgid "Choose this location"
msgstr "Elija esta ubicación"

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/location_schedule/location_schedule.js:0
msgid "Closed"
msgstr "Cerrado"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__company_id
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__company_id
msgid "Company"
msgstr "Compañía"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_form
msgid "Condition"
msgstr "Condición"

#. module: delivery
#: model:ir.model,name:delivery.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Content"
msgstr "Contenido"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__display_price
msgid "Cost"
msgstr "Coste"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__country_ids
msgid "Countries"
msgstr "Países"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__create_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__create_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__create_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__create_date
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__create_date
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__create_date
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__create_date
msgid "Created on"
msgstr "Creado el"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__currency_id
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__currency_id
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__currency_id
msgid "Currency"
msgstr "Moneda"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__partner_id
msgid "Customer"
msgstr "Cliente"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__debug_logging
msgid "Debug logging"
msgstr "Registro de depuración"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_res_partner__property_delivery_carrier_id
#: model:ir.model.fields,help:delivery.field_res_users__property_delivery_carrier_id
msgid "Default delivery method used in sales orders."
msgstr "Método de entrega por defecto utilizado en los pedidos de venta."

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_carrier_form
msgid "Define a new delivery method"
msgstr "Define un nuevo método de entrega."

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Delivery Carrier"
msgstr "Transportista"

#. module: delivery
#: model:ir.model,name:delivery.model_choose_delivery_carrier
msgid "Delivery Carrier Selection Wizard"
msgstr "Asistente de selección de transportista"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_form
msgid "Delivery Cost"
msgstr "Coste de entrega"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__delivery_message
#: model:ir.model.fields,field_description:delivery.field_sale_order__delivery_message
msgid "Delivery Message"
msgstr "Mensaje de entrega"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__name
#: model:ir.model.fields,field_description:delivery.field_res_partner__property_delivery_carrier_id
#: model:ir.model.fields,field_description:delivery.field_res_users__property_delivery_carrier_id
#: model:ir.model.fields,field_description:delivery.field_sale_order__carrier_id
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Delivery Method"
msgstr "Método de envío"

#. module: delivery
#: model:ir.actions.act_window,name:delivery.action_delivery_carrier_form
#: model:ir.ui.menu,name:delivery.sale_menu_action_delivery_carrier_form
msgid "Delivery Methods"
msgstr "Métodos de envío"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__delivery_price
msgid "Delivery Price"
msgstr "Precio de entrega"

#. module: delivery
#: model:ir.model,name:delivery.model_delivery_price_rule
msgid "Delivery Price Rules"
msgstr "Reglas de precios de entrega"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__product_id
msgid "Delivery Product"
msgstr "Producto de entrega"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__delivery_set
msgid "Delivery Set"
msgstr "Entrega establecida"

#. module: delivery
#: model:ir.model,name:delivery.model_delivery_zip_prefix
msgid "Delivery Zip Prefix"
msgstr "Prefijo de C. P. de entrega"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__recompute_delivery_price
#: model:ir.model.fields,field_description:delivery.field_sale_order_line__recompute_delivery_price
msgid "Delivery cost should be recomputed"
msgstr "El coste de envío debería ser recalculado"

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_zip_prefix_list
msgid ""
"Delivery zip prefixes are assigned to delivery carriers to restrict\n"
"            which zips it is available to."
msgstr ""
"Los prefijos postales de entrega se asignan a los transportistas de entrega para restringir\n"
"            para qué zonas está disponible."

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Description"
msgstr "Descripción"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Destination"
msgstr "Destino"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__sequence
msgid "Determine the display order"
msgstr "Determina el orden de visualización"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_carrier_view_form
msgid "Discard"
msgstr "Descartar"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__display_name
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__display_name
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__display_name
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_carrier_form
msgid ""
"Each carrier (e.g. UPS) can have several delivery methods (e.g.\n"
"            UPS Express, UPS Standard) with a set of pricing rules attached\n"
"            to each method."
msgstr ""
"Cada trasnportista (por ejemplo, UPS) puede tener varios métodos de entrega (por ejemplo,\n"
"            UPS Express, UPS Standard) con reglas de precios ya establecidas\n"
"            para cada método."

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__prod_environment
msgid "Environment"
msgstr "Entorno"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
msgid "Error: this delivery method is not available for this address."
msgstr "Error: este método de entrega no está disponible para esta dirección."

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
msgid "Error: this delivery method is not available."
msgstr "Error: este método de entrega no está disponible."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__invoice_policy
msgid ""
"Estimated Cost: the customer will be invoiced the estimated cost of the shipping.\n"
"Real Cost: the customer will be invoiced the real cost of the shipping, the cost of theshipping will be updated on the SO after the delivery."
msgstr ""
"Coste estimado: se le facturará al cliente el coste estimado del envío.\n"
"Coste real: se le facturará al cliente el coste real del envío y este se actualizará en el pedido de venta después de la entrega. "

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__invoice_policy__estimated
msgid "Estimated cost"
msgstr "Coste estimado"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__excluded_tag_ids
msgid "Excluded Tags"
msgstr "Etiquetas excluidas"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_sale_order__carrier_id
msgid "Fill this field if you plan to invoice the shipping based on picking."
msgstr ""
"Complete este campo si facturará el envío basado en el albarán del picking."

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid ""
"Filling this form allows you to make the shipping method available according"
" to the content of the order or its destination."
msgstr ""
"Completar este formulario le permitirá hacer el método de envío disponible "
"según el contenido del pedido o su destino."

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__fixed_margin
msgid "Fixed Margin"
msgstr "Márgen fijo "

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__fixed_price
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__delivery_type__fixed
msgid "Fixed Price"
msgstr "Precio fijo"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__free_over
msgid "Free if order amount is above"
msgstr "Gratis si el importe del pedido es superior a"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__return_label_on_delivery
msgid "Generate Return Label"
msgstr "Generar etiqueta de devolución"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__integration_level__rate
msgid "Get Rate"
msgstr "Obtener tarifa"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__integration_level__rate_and_ship
msgid "Get Rate and Create Shipment"
msgstr "Obtener tarifas y crear envío"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Group By"
msgstr "Agrupar por"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__id
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__id
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__id
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__id
msgid "ID"
msgstr "ID"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__free_over
msgid ""
"If the order total amount (shipping excluded) is above or equal to this "
"value, the customer benefits from a free shipping"
msgstr ""
"Si el importe total del pedido (gastos de envío excluidos) es igual o "
"superior a este valor, el envío es gratuito para el cliente."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__max_volume
msgid ""
"If the total volume of the order is over this volume, the method won't be "
"available."
msgstr ""
"Si el volumen total del pedido supera este volumen, el método no estará "
"disponible."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__max_weight
msgid ""
"If the total weight of the order is over this weight, the method won't be "
"available."
msgstr ""
"Si el peso total del pedido supera este peso, el método no estará "
"disponible."

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Install more Providers"
msgstr "Instalar más proveedores"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__shipping_insurance
msgid "Insurance Percentage"
msgstr "Porcentaje del seguro"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__integration_level
msgid "Integration Level"
msgstr "Nivel de integración"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__invoicing_message
msgid "Invoicing Message"
msgstr "Mensaje de facturación"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__invoice_policy
msgid "Invoicing Policy"
msgstr "Política de facturación"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order_line__is_delivery
msgid "Is a Delivery"
msgstr "Es una entrega"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__write_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__write_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__write_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__write_date
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__write_date
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__write_date
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
msgid "List view"
msgstr "Vista de lista"

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
msgid "Loading..."
msgstr "Cargando…"

#. module: delivery
#: model:delivery.carrier,name:delivery.delivery_local_delivery
msgid "Local Delivery"
msgstr "Entrega local"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__debug_logging
msgid "Log requests in order to ease debugging"
msgstr "Solicitudes de registro para facilitar la depuración"

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_zip_prefix_list
msgid "Manage delivery zip prefixes"
msgstr "Gestionar prefijos de C. P. de entrega"

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
msgid "Map view"
msgstr "Vista de mapa"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__margin
msgid "Margin"
msgstr "Margen"

#. module: delivery
#: model:ir.model.constraint,message:delivery.constraint_delivery_carrier_margin_not_under_100_percent
msgid "Margin cannot be lower than -100%"
msgstr "El margen no puede ser inferior a -100 %"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Margin on Rate"
msgstr "Margen en la tarifa"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__max_volume
msgid "Max Volume"
msgstr "Volumen máximo"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__max_weight
msgid "Max Weight"
msgstr "Peso máximo"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__max_value
msgid "Maximum Value"
msgstr "Valor máximo"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__must_have_tag_ids
msgid "Must Have Tags"
msgstr "Etiquetas que deben existir"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__name
msgid "Name"
msgstr "Nombre"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
msgid "New Providers"
msgstr "Nuevos proveedores"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/sale_order.py:0
msgid "No pick-up points are available for this delivery address."
msgstr "No hay puntos de recogida disponibles para esta dirección de entrega."

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
msgid "No result"
msgstr "Sin resultado"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
msgid "Not available for current order"
msgstr "No disponible para la orden actual "

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/location/location.js:0
#: code:addons/delivery/static/src/js/location_selector/map_container/map_container.js:0
msgid "Opening hours"
msgstr "Horario de apertura"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__operator
msgid "Operator"
msgstr "Operador"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__order_id
msgid "Order"
msgstr "Pedido"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__pickup_location_data
msgid "Pickup Location Data"
msgstr "Información de ubicación de recogida"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Please select a country before choosing a state or a zip prefix."
msgstr "Seleccione un país antes de elegir un estado o un prefijo de C.P. "

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__name
msgid "Prefix"
msgstr "Prefijo"

#. module: delivery
#: model:ir.model.constraint,message:delivery.constraint_delivery_zip_prefix_name_uniq
msgid "Prefix already exists!"
msgstr "¡Este prefijo ya existe!"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__zip_prefix_ids
msgid ""
"Prefixes of zip codes that this carrier applies to. Note that regular "
"expressions can be used to support countries with varying zip code lengths, "
"i.e. '$' can be added to end of prefix to match the exact zip (e.g. '100$' "
"will only match '100' and not '1000')"
msgstr ""
"Prefijos de códigos postales que aplican para este transportista. Tome en "
"cuenta que las expresiones comunes se pueden utilizar para la compatibilidad"
" con países con longitudes de códigos postales variantes. Es decir, puede "
"añadir \"$\" al final del prefijo para que coincida con el código postal "
"exacto (por ejemplo, \"100 $\" solo coincidirá con \"100\" y no con "
"\"1000\")"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__price
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__price
msgid "Price"
msgstr "Precio"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_form
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_tree
msgid "Price Rules"
msgstr "Reglas de precio"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Pricing"
msgstr "Precio"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__price_rule_ids
msgid "Pricing Rules"
msgstr "Reglas de precios"

#. module: delivery
#: model:ir.model,name:delivery.model_product_category
msgid "Product Category"
msgstr "Categoría de producto"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order_line__product_qty
msgid "Product Qty"
msgstr "Ctd. de producto"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__delivery_type
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__delivery_type
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Provider"
msgstr "Proveedor"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__quantity
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__quantity
msgid "Quantity"
msgstr "Cantidad"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__get_return_label_from_portal
msgid "Return Label Accessible from Customer Portal"
msgstr "Etiqueta de devolución accesible desde el portal de clientes"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__list_base_price
msgid "Sale Base Price"
msgstr "Precio base de venta"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__list_price
msgid "Sale Price"
msgstr "Precio de venta"

#. module: delivery
#: model:ir.model,name:delivery.model_sale_order
msgid "Sales Order"
msgstr "Pedido de venta"

#. module: delivery
#: model:ir.model,name:delivery.model_sale_order_line
msgid "Sales Order Line"
msgstr "Línea de pedido de venta"

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.xml:0
msgid "Search"
msgstr "Buscar"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__sequence
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__is_all_service
msgid "Service Product"
msgstr "Producto de servicio"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__prod_environment
msgid "Set to True if your credentials are certified for production."
msgstr ""
"Establezca como \"True\" si sus credenciales están certificadas para "
"producción."

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__carrier_id
msgid "Shipping Method"
msgstr "Método de envío"

#. module: delivery
#: model:ir.model,name:delivery.model_delivery_carrier
#: model_terms:ir.ui.view,arch_db:delivery.res_config_settings_view_form
msgid "Shipping Methods"
msgstr "Métodos de envío"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__shipping_weight
msgid "Shipping Weight"
msgstr "Peso de envío"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__shipping_insurance
msgid ""
"Shipping insurance is a service which may reimburse senders whose parcels "
"are lost, stolen, and/or damaged in transit."
msgstr ""
"El seguro de envío es un servicio que puede reembolsar a los remitentes en "
"caso de que sus paquetes se pierdan, sean robados o se dañen en el camino."

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid ""
"Shipping method details to be included at bottom sales orders and their "
"confirmation emails. E.g. Instructions for customers to follow."
msgstr ""
"Detalles del método de envío que se incluirán en la parte inferior de los "
"pedidos de venta y sus correos electrónicos de confirmación. Por ejemplo, "
"instrucciones que los clientes deben seguir."

#. module: delivery
#: model:delivery.carrier,name:delivery.free_delivery_carrier
#: model:product.template,name:delivery.product_product_delivery_product_template
msgid "Standard delivery"
msgstr "Entrega estándar"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__state_ids
msgid "States"
msgstr "Provincias"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__supports_shipping_insurance
msgid "Supports Shipping Insurance"
msgstr "Es compatible con seguro de envío"

#. module: delivery
#: model:delivery.carrier,name:delivery.delivery_carrier
#: model:product.template,name:delivery.product_product_delivery_poste_product_template
msgid "The Poste"
msgstr "The Poste"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__excluded_tag_ids
msgid ""
"The method is NOT available if at least one product of the order has one of "
"these tags."
msgstr ""
"El método NO está disponible si al menos un producto del pedido tiene una de"
" estas etiquetas."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__must_have_tag_ids
msgid ""
"The method is available only if at least one product of the order has one of"
" these tags."
msgstr ""
"El método estará disponible solo si al menos un producto del pedido tiene "
"estas etiquetas."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__get_return_label_from_portal
msgid ""
"The return label can be downloaded by the customer from the customer portal."
msgstr ""
"El cliente puede descargar la etiqueta de devolución desde el portal de "
"clientes."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__return_label_on_delivery
msgid "The return label is automatically generated at the delivery."
msgstr ""
"La etiqueta de devolución se genera automáticamente al hacerse el envío."

#. module: delivery
#: model:ir.model.constraint,message:delivery.constraint_delivery_carrier_shipping_insurance_is_percentage
msgid "The shipping insurance must be a percentage between 0 and 100."
msgstr "El seguro de envío debe ser un porcentaje entre 0 y 100."

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
msgid "The shipping is free since the order amount exceeds %.2f."
msgstr "El envío es gratuito, ya que el importe del pedido es mayor a %.2f."

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/map_container/map_container.js:0
msgid "There was an error loading the map"
msgstr "Ocurrió un error al cargar el mapa"

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_carrier_form
msgid ""
"These methods allow to automatically compute the delivery price\n"
"            according to your settings; on the sales order (based on the\n"
"            quotation) or the invoice (based on the delivery orders)."
msgstr ""
"Estos métodos permiten calcular automáticamente el precio de entrega\n"
"            de acuerdo a sus ajustes; en el pedido de venta (basado en el\n"
"            presupuesto) o en la factura (basade en los pedidos de entrega)."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__fixed_margin
msgid "This fixed amount will be added to the shipping price."
msgstr "Este importe fijo se añadirá al precio de envío."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__tracking_url
msgid ""
"This option adds a link for the customer in the portal to track their "
"package easily. Use <shipmenttrackingnumber> as a placeholder in your URL."
msgstr ""
"Esta opción añade un enlace para el cliente en el portal para rastrear el "
"paquete sin problemas. Use <shipmenttrackingnumber> como un marcador de "
"posición en su URL."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__margin
msgid "This percentage will be added to the shipping price."
msgstr "Este porcentaje se añadirá al precio de envío."

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__total_weight
msgid "Total Order Weight"
msgstr "Peso total de la orden"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__tracking_url
msgid "Tracking Link"
msgstr "Enlace de rastreo"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_carrier_view_form
msgid "Update"
msgstr "Actualizar"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/sale_order.py:0
#: model_terms:ir.ui.view,arch_db:delivery.view_order_form_with_carrier
msgid "Update shipping cost"
msgstr "Actualizar coste de envío"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__variable
msgid "Variable"
msgstr "Variable"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__variable_factor
msgid "Variable Factor"
msgstr "Factor variable"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__volume
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__volume
msgid "Volume"
msgstr "Volumen"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__volume_uom_name
msgid "Volume unit of measure label"
msgstr "Etiqueta de unidad de medida de volumen"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__weight
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__weight
msgid "Weight"
msgstr "Peso"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__wv
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__wv
msgid "Weight * Volume"
msgstr "Peso * Volumen"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__weight_uom_name
msgid "Weight Uom Name"
msgstr "Nombre de la unidad de medida del peso"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__weight_uom_name
msgid "Weight unit of measure label"
msgstr "Etiqueta de unidad de medida de peso"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/sale_order.py:0
msgid ""
"You can not update the shipping costs on an order where it was already invoiced!\n"
"\n"
"The following delivery lines (product, invoiced quantity and price) have already been processed:\n"
"\n"
msgstr ""
"¡No puede actualizar los costes de envío de una orden que ya fue facturada!\n"
"\n"
"Las siguientes líneas de entrega (producto, cantidad facturada y precio) ya fueron procesadas:\n"
"\n"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/product_category.py:0
msgid ""
"You cannot delete the deliveries product category as it is used on the "
"delivery carriers products."
msgstr ""
"No puede eliminar la categoría de productos \"Entregas\", se utiliza en los "
"productos de transportistas de envío."

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
msgid "Your postal code"
msgstr "Su código postal"

#. module: delivery
#: model:ir.actions.act_window,name:delivery.action_delivery_zip_prefix_list
msgid "Zip Prefix"
msgstr "Prefijo de C. P."

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__zip_prefix_ids
msgid "Zip Prefixes"
msgstr "Prefijos de C. P."

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "e.g. UPS Express"
msgstr "p. ej. UPS Express"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "i.e. https://ekartlogistics.com/shipmenttrack/<shipmenttrackingnumber>"
msgstr ""
"es decir, https://ekartlogistics.com/shipmenttrack/<shipmenttrackingnumber>"
