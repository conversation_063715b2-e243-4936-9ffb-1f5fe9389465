# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* google_gmail
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.fetchmail_server_view_form
#: model_terms:ir.ui.view,arch_db:google_gmail.ir_mail_server_view_form
msgid "<i class=\"fa fa-cog\" title=\"Edit Settings\"/>"
msgstr "<i class=\"fa fa-cog\" title=\"Edit Settings\"/>"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.fetchmail_server_view_form
#: model_terms:ir.ui.view,arch_db:google_gmail.ir_mail_server_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                        Connect your Gmail account"
msgstr ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                        Povežite svoj Gmail račun"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.fetchmail_server_view_form
msgid ""
"<span invisible=\"server_type != 'gmail' or not google_gmail_refresh_token\" class=\"badge text-bg-success\">\n"
"                        Gmail Token Valid\n"
"                    </span>"
msgstr ""
"<span invisible=\"server_type != 'gmail' or not google_gmail_refresh_token\" class=\"badge text-bg-success\">\n"
"                        Veljaven žeton Gmail\n"
"                    </span>"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.ir_mail_server_view_form
msgid ""
"<span invisible=\"smtp_authentication != 'gmail' or not google_gmail_refresh_token\" class=\"badge text-bg-success\">\n"
"                        Gmail Token Valid\n"
"                    </span>"
msgstr ""
"<span invisible=\"smtp_authentication != 'gmail' or not google_gmail_refresh_token\" class=\"badge text-bg-success\">\n"
"                        Veljaven žeton Gmail\n"
"                    </span>"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_fetchmail_server__google_gmail_access_token
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__google_gmail_access_token
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__google_gmail_access_token
msgid "Access Token"
msgstr "Žeton vabila"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_fetchmail_server__google_gmail_access_token_expiration
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__google_gmail_access_token_expiration
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__google_gmail_access_token_expiration
msgid "Access Token Expiration Timestamp"
msgstr "Časovni žig poteka dostopnega žetona"

#. module: google_gmail
#. odoo-python
#: code:addons/google_gmail/controllers/main.py:0
msgid "An error occur during the authentication process."
msgstr "Med postopkom preverjanja pristnosti je prišlo do napake."

#. module: google_gmail
#. odoo-python
#: code:addons/google_gmail/models/google_gmail_mixin.py:0
msgid "An error occurred when fetching the access token."
msgstr "Pri pridobivanju dostopnega žetona je prišlo do napake."

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__smtp_authentication
msgid "Authenticate with"
msgstr "Preverjanje pristnosti z"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_fetchmail_server__google_gmail_authorization_code
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__google_gmail_authorization_code
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__google_gmail_authorization_code
msgid "Authorization Code"
msgstr "Koda za odobritev"

#. module: google_gmail
#: model:ir.model,name:google_gmail.model_res_config_settings
msgid "Config Settings"
msgstr "Uredi nastavitve"

#. module: google_gmail
#. odoo-python
#: code:addons/google_gmail/models/fetchmail_server.py:0
msgid ""
"Connect your Gmail account with the OAuth Authentication process. \n"
"You will be redirected to the Gmail login page where you will need to accept the permission."
msgstr ""
"Povežite svoj Gmail račun s postopkom overjanja OAuth. \n"
"Preusmerjeni boste na prijavno stran Gmaila, kjer boste morali sprejeti dovoljenje."

#. module: google_gmail
#. odoo-python
#: code:addons/google_gmail/models/ir_mail_server.py:0
msgid ""
"Connect your Gmail account with the OAuth Authentication process.  \n"
"By default, only a user with a matching email address will be able to use this server. To extend its use, you should set a \"mail.default.from\" system parameter."
msgstr ""
"Povežite svoj Gmail račun s postopkom preverjanja pristnosti OAuth.  \n"
"Privzeto bo ta strežnik lahko uporabljal le uporabnik z ustreznim e-poštnim naslovom. Če želite razširiti njegovo uporabo, morate nastaviti sistemski parameter »mail.default.from«."

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_res_config_settings__google_gmail_client_identifier
msgid "Gmail Client Id"
msgstr "ID odjemalca Gmail"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_res_config_settings__google_gmail_client_secret
msgid "Gmail Client Secret"
msgstr "Skrivnost odjemalca Gmail"

#. module: google_gmail
#: model:ir.model.fields.selection,name:google_gmail.selection__fetchmail_server__server_type__gmail
#: model:ir.model.fields.selection,name:google_gmail.selection__ir_mail_server__smtp_authentication__gmail
msgid "Gmail OAuth Authentication"
msgstr "Preverjanje pristnosti OAuth v Gmailu"

#. module: google_gmail
#: model:ir.model,name:google_gmail.model_google_gmail_mixin
msgid "Google Gmail Mixin"
msgstr "Google Gmail Mixin"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.res_config_settings_view_form
msgid "ID"
msgstr "ID"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.res_config_settings_view_form
msgid "ID of your Google app"
msgstr "ID vaše Googlove aplikacije"

#. module: google_gmail
#: model:ir.model,name:google_gmail.model_fetchmail_server
msgid "Incoming Mail Server"
msgstr "Strežnih vhodne pošte"

#. module: google_gmail
#. odoo-python
#: code:addons/google_gmail/models/ir_mail_server.py:0
msgid ""
"Incorrect Connection Security for Gmail mail server “%s”. Please set it to "
"\"TLS (STARTTLS)\"."
msgstr ""
"Nepravilna varnost povezave za poštni strežnik Gmail “%s”. Nastavite jo na "
"»TLS (STARTTLS)«."

#. module: google_gmail
#: model:ir.model,name:google_gmail.model_ir_mail_server
msgid "Mail Server"
msgstr "Poštni strežnik"

#. module: google_gmail
#. odoo-python
#: code:addons/google_gmail/models/google_gmail_mixin.py:0
msgid "Only the administrator can link a Gmail mail server."
msgstr "Samo skrbnik lahko poveže poštni strežnik Gmail."

#. module: google_gmail
#. odoo-python
#: code:addons/google_gmail/models/google_gmail_mixin.py:0
msgid "Please configure your Gmail credentials."
msgstr "Konfigurirajte svoje poverilnice za Gmail."

#. module: google_gmail
#. odoo-python
#: code:addons/google_gmail/models/ir_mail_server.py:0
msgid ""
"Please fill the \"Username\" field with your Gmail username (your email "
"address). This should be the same account as the one used for the Gmail "
"OAuthentication Token."
msgstr ""
"V polje »Uporabniško ime« vnesite svoje uporabniško ime za Gmail (vaš "
"e-poštni naslov). To mora biti isti račun, kot je tisti, ki ste ga uporabili"
" za žeton Gmail OAuthentication."

#. module: google_gmail
#. odoo-python
#: code:addons/google_gmail/models/ir_mail_server.py:0
msgid ""
"Please leave the password field empty for Gmail mail server “%s”. The OAuth "
"process does not require it"
msgstr ""
"Polje za geslo za Gmail poštni strežnik pustite prazno.“%s”. Postopek OAuth "
"tega ne zahteva"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.ir_mail_server_view_form
msgid "Read More"
msgstr "Preberi več"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_fetchmail_server__google_gmail_refresh_token
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__google_gmail_refresh_token
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__google_gmail_refresh_token
msgid "Refresh Token"
msgstr "Osvežitev žetona"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.res_config_settings_view_form
msgid "Secret"
msgstr "Skrivnost"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.res_config_settings_view_form
msgid "Secret of your Google app"
msgstr "Skrivnost vaše aplikacije Google"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_fetchmail_server__server_type
msgid "Server Type"
msgstr "Tip Strežnika"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.fetchmail_server_view_form
#: model_terms:ir.ui.view,arch_db:google_gmail.ir_mail_server_view_form
msgid ""
"Setup your Gmail API credentials in the general settings to link a Gmail "
"account."
msgstr ""
"V splošnih nastavitvah nastavite poverilnice za Gmail API, da povežete Gmail"
" račun."

#. module: google_gmail
#: model:ir.model.fields,help:google_gmail.field_fetchmail_server__google_gmail_uri
#: model:ir.model.fields,help:google_gmail.field_google_gmail_mixin__google_gmail_uri
#: model:ir.model.fields,help:google_gmail.field_ir_mail_server__google_gmail_uri
msgid "The URL to generate the authorization code from Google"
msgstr "URL povezava za ustvarjanje kode za overitev"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_fetchmail_server__google_gmail_uri
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__google_gmail_uri
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__google_gmail_uri
msgid "URI"
msgstr "URI"
