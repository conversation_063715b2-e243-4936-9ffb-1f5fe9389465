# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* certificate
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# Wil Odoo, 2024
# <PERSON>risa_nexterp, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:39+0000\n"
"PO-Revision-Date: 2024-09-29 00:00+0000\n"
"Last-Translator: Larisa_nexterp, 2025\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/key.py:0
msgid "A private key is required to decrypt data."
msgstr "O cheie privată este necesară pentru a decripta datele."

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__active
#: model:ir.model.fields,field_description:certificate.field_certificate_key__active
msgid "Active"
msgstr "Activ"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.res_config_settings_view_form
msgid "Add, edit and delete certificates."
msgstr "Adăugați, editați și ștergeți certificate."

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.res_config_settings_view_form
msgid "Add, edit and delete keys."
msgstr "Adăugați, editați și ștergeți chei."

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_search
#: model_terms:ir.ui.view,arch_db:certificate.certificate_key_view_search
msgid "Archived"
msgstr "Arhivat"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_search
msgid "Archived certificates"
msgstr "Certificate arhivate"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_key_view_search
msgid "Archived keys"
msgstr "Chei arhivate"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__date_start
msgid "Available date"
msgstr "Dată disponibilă"

#. module: certificate
#: model:ir.model,name:certificate.model_certificate_certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__content
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_list
msgid "Certificate"
msgstr "Certificat"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__pkcs12_password
msgid "Certificate Password"
msgstr "Parolă certificat"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__pem_certificate
msgid "Certificate in PEM format"
msgstr "Certificat în format PEM"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__scope
msgid "Certificate scope"
msgstr "Domeniu certificat"

#. module: certificate
#: model:ir.actions.act_window,name:certificate.certificate_certificate_action_view_list
#: model_terms:ir.ui.view,arch_db:certificate.res_config_settings_view_form
msgid "Certificates"
msgstr "Certificate"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.res_config_settings_view_form
msgid "Certificates and Keys"
msgstr "Certificate și Chei"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__company_id
#: model:ir.model.fields,field_description:certificate.field_certificate_key__company_id
msgid "Company"
msgstr "Companie"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__country_code
msgid "Country Code"
msgstr "Cod țară"

#. module: certificate
#: model_terms:ir.actions.act_window,help:certificate.certificate_certificate_action_view_list
msgid "Create a first certificate"
msgstr "Creați primul certificat"

#. module: certificate
#: model_terms:ir.actions.act_window,help:certificate.certificate_key_action_view_list
msgid "Create a first key"
msgstr "Creați prima cheie"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__create_uid
#: model:ir.model.fields,field_description:certificate.field_certificate_key__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__create_date
#: model:ir.model.fields,field_description:certificate.field_certificate_key__create_date
msgid "Created on"
msgstr "Creat pe"

#. module: certificate
#: model:ir.model,name:certificate.model_certificate_key
msgid "Cryptographic Keys"
msgstr "Chei criptografice"

#. module: certificate
#: model:ir.model.fields.selection,name:certificate.selection__certificate_certificate__content_format__der
msgid "DER"
msgstr "DER"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__display_name
#: model:ir.model.fields,field_description:certificate.field_certificate_key__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__date_end
msgid "Expiration date"
msgstr "Data expirării"

#. module: certificate
#: model:ir.model.fields.selection,name:certificate.selection__certificate_certificate__scope__general
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_search
msgid "General"
msgstr "General"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_search
msgid "General certificates"
msgstr "Certificate generale"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__id
#: model:ir.model.fields,field_description:certificate.field_certificate_key__id
msgid "ID"
msgstr "ID"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_search
#: model_terms:ir.ui.view,arch_db:certificate.certificate_key_view_search
msgid "Invalid"
msgstr "Nevalid"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_key_view_list
msgid "Key"
msgstr "Cheie"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_key__pem_key
msgid "Key bytes in PEM format"
msgstr "Octeți cheie în format PEM"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_key__content
msgid "Key file"
msgstr "Fișier cheie"

#. module: certificate
#: model:ir.actions.act_window,name:certificate.certificate_key_action_view_list
#: model_terms:ir.ui.view,arch_db:certificate.res_config_settings_view_form
msgid "Keys"
msgstr "Chei"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__write_uid
#: model:ir.model.fields,field_description:certificate.field_certificate_key__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__write_date
#: model:ir.model.fields,field_description:certificate.field_certificate_key__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__loading_error
#: model:ir.model.fields,field_description:certificate.field_certificate_key__loading_error
msgid "Loading error"
msgstr "Eroare la încărcare"

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/key.py:0
msgid "Make sure to use a private key to sign documents."
msgstr "Asigurați-vă că folosiți o cheie privată pentru a semna documente."

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.res_config_settings_view_form
msgid "Manage your certificates"
msgstr "Gestionați certificatele"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.res_config_settings_view_form
msgid "Manage your keys"
msgstr "Gestionați cheile"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__name
#: model:ir.model.fields,field_description:certificate.field_certificate_key__name
msgid "Name"
msgstr "Nume"

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/certificate.py:0
msgid ""
"No private key linked to the certificate, it is required to sign documents."
msgstr ""
"Nu există nicio cheie privată asociată certificatului, este necesară pentru "
"a semna documente."

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_search
msgid "Not valid certificates"
msgstr "Certificate nevalide"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__content_format
msgid "Original certificate format"
msgstr "Format certificat original"

#. module: certificate
#: model:ir.model.fields.selection,name:certificate.selection__certificate_certificate__content_format__pem
msgid "PEM"
msgstr "PEM"

#. module: certificate
#: model:ir.model.fields.selection,name:certificate.selection__certificate_certificate__content_format__pkcs12
msgid "PKCS12"
msgstr "PKCS12"

#. module: certificate
#: model:ir.model.fields,help:certificate.field_certificate_certificate__pkcs12_password
msgid "Password to decrypt the PKS file."
msgstr "Parolă pentru decriptarea fișierului PKS."

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_key_view_search
msgid "Private"
msgstr "Privat"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__private_key_id
msgid "Private Key"
msgstr "Cheie privată"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_key__password
msgid "Private key password"
msgstr "Parolă cheie privată"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_key_view_search
msgid "Public"
msgstr "Public"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__public_key_id
msgid "Public Key"
msgstr "Cheie publică"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_key__public
msgid "Public/Private key"
msgstr "Cheie publică/privată"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__serial_number
msgid "Serial number"
msgstr "Număr de serie"

#. module: certificate
#: model:ir.model.fields,help:certificate.field_certificate_certificate__active
msgid "Set active to false to archive the certificate"
msgstr "Setați activ la fals pentru a arhiva certificatul"

#. module: certificate
#: model:ir.model.fields,help:certificate.field_certificate_key__active
msgid "Set active to false to archive the key."
msgstr "Setați activ la fals pentru a arhiva cheia."

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__subject_common_name
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_list
msgid "Subject Name"
msgstr "Nume subiect"

#. module: certificate
#: model:ir.model.fields,help:certificate.field_certificate_certificate__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"Codul ISO al țării din două caractere.\n"
"Puteți utiliza acest câmp pentru căutări rapide."

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/certificate.py:0
msgid "The certificate and private key are not compatible."
msgstr "Certificatul și cheia privată nu sunt compatibile."

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/certificate.py:0
msgid "The certificate and public key are not compatible."
msgstr "Certificatul și cheia publică nu sunt compatibile."

#. module: certificate
#: model:ir.model.fields,help:certificate.field_certificate_certificate__date_end
msgid "The date on which the certificate expires (UTC)"
msgstr "Data la care expiră certificatul (UTC)"

#. module: certificate
#: model:ir.model.fields,help:certificate.field_certificate_certificate__date_start
msgid "The date on which the certificate starts to be valid (UTC)"
msgstr "Data la care certificatul devine valabil (UTC)"

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/key.py:0
msgid "The key size should be at least 512 bytes."
msgstr "Dimensiunea cheii trebuie să fie de cel puțin 512 octeți."

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/key.py:0
msgid "The private key could not be loaded."
msgstr "Cheia privată nu a putut fi încărcată."

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/key.py:0
msgid "The public exponent should be 65537 (or 3 for legacy purposes)."
msgstr ""
"Exponentul public trebuie să fie 65537 (sau 3 pentru compatibilitate veche)."

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/key.py:0
msgid "The public key could not be loaded."
msgstr "Cheia publică nu a putut fi încărcată."

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/certificate.py:0
msgid "The public key from the certificate could not be loaded."
msgstr "Cheia publică din certificat nu a putut fi încărcată."

#. module: certificate
#: model:ir.model.fields,help:certificate.field_certificate_certificate__serial_number
msgid "The serial number to add to electronic documents"
msgstr "Numărul de serie de adăugat la documentele electronice"

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/certificate.py:0
msgid ""
"This certificate could not be loaded. Either the content or the password is "
"erroneous."
msgstr ""
"Acest certificat nu a putut fi încărcat. Conținutul sau parola sunt eronate."

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/certificate.py:0
msgid "This certificate is not valid, its validity has expired."
msgstr "Acest certificat nu este valabil, valabilitatea sa a expirat."

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/key.py:0
msgid ""
"This key could not be loaded. Either its content or its password is "
"erroneous."
msgstr ""
"Această cheie nu a putut fi încărcată. Conținutul sau parola sunt eronate."

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/key.py:0
msgid ""
"Unsupported asymmetric cryptography algorithm '%s'. Currently supported for "
"decryption: RSA."
msgstr ""
"Algoritm de criptografie asimetrică nesuportat '%s'. În prezent este "
"suportat pentru decriptare: RSA."

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/key.py:0
msgid ""
"Unsupported asymmetric cryptography algorithm '%s'. Currently supported for "
"signature: EC and RSA."
msgstr ""
"Algoritm de criptografie asimetrică nesuportat '%s'. În prezent este "
"suportat pentru semnătură: EC și RSA."

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/key.py:0
msgid ""
"Unsupported asymmetric cryptography algorithm '%s'. Currently supported: EC,"
" RSA."
msgstr ""
"Algoritm de criptografie asimetrică nesuportat '%s'. În prezent sunt "
"suportate: EC, RSA."

#. module: certificate
#: model:ir.model.fields,help:certificate.field_certificate_certificate__public_key_id
msgid ""
"Used to set a public key in case the one self-contained in the certificate is erroneus.\n"
"                When a public key is set this way, it will be used instead of the one in the certificate.\n"
"             "
msgstr ""
"Folosit pentru a seta o cheie publică în cazul în care cea inclusă în certificat este eronată.\n"
"Când o cheie publică este setată astfel, aceasta va fi folosită în locul celei din certificat.\n"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__is_valid
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_search
msgid "Valid"
msgstr "Valabil"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_search
msgid "Valid certificates"
msgstr "Certificate valabile"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_form
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_list
msgid "Validity"
msgstr "Valabilitate"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_form
msgid "certificate form"
msgstr "formular certificat"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_list
msgid "certificate list"
msgstr "listă certificate"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_search
msgid "certificate search"
msgstr "căutare certificat"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_form
msgid "e.g. New Certificate"
msgstr "ex: Certificat Nou"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_key_view_form
msgid "key form"
msgstr "formular cheie"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_key_view_list
msgid "key list"
msgstr "listă chei"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_key_view_search
msgid "key search"
msgstr "căutare cheie"
