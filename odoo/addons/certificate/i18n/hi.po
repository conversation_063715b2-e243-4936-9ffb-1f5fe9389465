# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* certificate
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2025
# <PERSON><PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:39+0000\n"
"PO-Revision-Date: 2024-09-29 00:00+0000\n"
"Last-Translator: Uj<PERSON><PERSON> Pathak, 2025\n"
"Language-Team: Hindi (https://app.transifex.com/odoo/teams/41243/hi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/key.py:0
msgid "A private key is required to decrypt data."
msgstr ""

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__active
#: model:ir.model.fields,field_description:certificate.field_certificate_key__active
msgid "Active"
msgstr "सक्रिय"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.res_config_settings_view_form
msgid "Add, edit and delete certificates."
msgstr ""

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.res_config_settings_view_form
msgid "Add, edit and delete keys."
msgstr ""

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_search
#: model_terms:ir.ui.view,arch_db:certificate.certificate_key_view_search
msgid "Archived"
msgstr "संग्रहीत किया गया"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_search
msgid "Archived certificates"
msgstr ""

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_key_view_search
msgid "Archived keys"
msgstr ""

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__date_start
msgid "Available date"
msgstr ""

#. module: certificate
#: model:ir.model,name:certificate.model_certificate_certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__content
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_list
msgid "Certificate"
msgstr ""

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__pkcs12_password
msgid "Certificate Password"
msgstr ""

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__pem_certificate
msgid "Certificate in PEM format"
msgstr ""

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__scope
msgid "Certificate scope"
msgstr ""

#. module: certificate
#: model:ir.actions.act_window,name:certificate.certificate_certificate_action_view_list
#: model_terms:ir.ui.view,arch_db:certificate.res_config_settings_view_form
msgid "Certificates"
msgstr ""

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.res_config_settings_view_form
msgid "Certificates and Keys"
msgstr ""

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__company_id
#: model:ir.model.fields,field_description:certificate.field_certificate_key__company_id
msgid "Company"
msgstr "संस्था"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__country_code
msgid "Country Code"
msgstr "देश का कोड"

#. module: certificate
#: model_terms:ir.actions.act_window,help:certificate.certificate_certificate_action_view_list
msgid "Create a first certificate"
msgstr ""

#. module: certificate
#: model_terms:ir.actions.act_window,help:certificate.certificate_key_action_view_list
msgid "Create a first key"
msgstr ""

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__create_uid
#: model:ir.model.fields,field_description:certificate.field_certificate_key__create_uid
msgid "Created by"
msgstr "द्वारा निर्मित"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__create_date
#: model:ir.model.fields,field_description:certificate.field_certificate_key__create_date
msgid "Created on"
msgstr "इस तारीख को बनाया गया"

#. module: certificate
#: model:ir.model,name:certificate.model_certificate_key
msgid "Cryptographic Keys"
msgstr ""

#. module: certificate
#: model:ir.model.fields.selection,name:certificate.selection__certificate_certificate__content_format__der
msgid "DER"
msgstr ""

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__display_name
#: model:ir.model.fields,field_description:certificate.field_certificate_key__display_name
msgid "Display Name"
msgstr "डिस्प्ले नाम"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__date_end
msgid "Expiration date"
msgstr ""

#. module: certificate
#: model:ir.model.fields.selection,name:certificate.selection__certificate_certificate__scope__general
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_search
msgid "General"
msgstr "सामान्य"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_search
msgid "General certificates"
msgstr ""

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__id
#: model:ir.model.fields,field_description:certificate.field_certificate_key__id
msgid "ID"
msgstr "आईडी"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_search
#: model_terms:ir.ui.view,arch_db:certificate.certificate_key_view_search
msgid "Invalid"
msgstr "अमान्य"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_key_view_list
msgid "Key"
msgstr ""

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_key__pem_key
msgid "Key bytes in PEM format"
msgstr ""

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_key__content
msgid "Key file"
msgstr ""

#. module: certificate
#: model:ir.actions.act_window,name:certificate.certificate_key_action_view_list
#: model_terms:ir.ui.view,arch_db:certificate.res_config_settings_view_form
msgid "Keys"
msgstr ""

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__write_uid
#: model:ir.model.fields,field_description:certificate.field_certificate_key__write_uid
msgid "Last Updated by"
msgstr "इन्होंने आखिरी बार अपडेट किया"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__write_date
#: model:ir.model.fields,field_description:certificate.field_certificate_key__write_date
msgid "Last Updated on"
msgstr "आखिरी बार अपडेट हुआ"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__loading_error
#: model:ir.model.fields,field_description:certificate.field_certificate_key__loading_error
msgid "Loading error"
msgstr ""

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/key.py:0
msgid "Make sure to use a private key to sign documents."
msgstr ""

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.res_config_settings_view_form
msgid "Manage your certificates"
msgstr ""

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.res_config_settings_view_form
msgid "Manage your keys"
msgstr ""

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__name
#: model:ir.model.fields,field_description:certificate.field_certificate_key__name
msgid "Name"
msgstr "नाम"

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/certificate.py:0
msgid ""
"No private key linked to the certificate, it is required to sign documents."
msgstr ""

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_search
msgid "Not valid certificates"
msgstr ""

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__content_format
msgid "Original certificate format"
msgstr ""

#. module: certificate
#: model:ir.model.fields.selection,name:certificate.selection__certificate_certificate__content_format__pem
msgid "PEM"
msgstr ""

#. module: certificate
#: model:ir.model.fields.selection,name:certificate.selection__certificate_certificate__content_format__pkcs12
msgid "PKCS12"
msgstr ""

#. module: certificate
#: model:ir.model.fields,help:certificate.field_certificate_certificate__pkcs12_password
msgid "Password to decrypt the PKS file."
msgstr ""

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_key_view_search
msgid "Private"
msgstr ""

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__private_key_id
msgid "Private Key"
msgstr ""

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_key__password
msgid "Private key password"
msgstr ""

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_key_view_search
msgid "Public"
msgstr ""

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__public_key_id
msgid "Public Key"
msgstr ""

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_key__public
msgid "Public/Private key"
msgstr ""

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__serial_number
msgid "Serial number"
msgstr ""

#. module: certificate
#: model:ir.model.fields,help:certificate.field_certificate_certificate__active
msgid "Set active to false to archive the certificate"
msgstr ""

#. module: certificate
#: model:ir.model.fields,help:certificate.field_certificate_key__active
msgid "Set active to false to archive the key."
msgstr ""

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__subject_common_name
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_list
msgid "Subject Name"
msgstr ""

#. module: certificate
#: model:ir.model.fields,help:certificate.field_certificate_certificate__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"यह आईएसओ देश का दो अक्षर वाला कोड है. \n"
"इसे आप जल्दी कुछ ढूंढने के लिए इस्तेमाल कर सकते हैं"

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/certificate.py:0
msgid "The certificate and private key are not compatible."
msgstr ""

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/certificate.py:0
msgid "The certificate and public key are not compatible."
msgstr ""

#. module: certificate
#: model:ir.model.fields,help:certificate.field_certificate_certificate__date_end
msgid "The date on which the certificate expires (UTC)"
msgstr ""

#. module: certificate
#: model:ir.model.fields,help:certificate.field_certificate_certificate__date_start
msgid "The date on which the certificate starts to be valid (UTC)"
msgstr ""

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/key.py:0
msgid "The key size should be at least 512 bytes."
msgstr ""

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/key.py:0
msgid "The private key could not be loaded."
msgstr ""

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/key.py:0
msgid "The public exponent should be 65537 (or 3 for legacy purposes)."
msgstr ""

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/key.py:0
msgid "The public key could not be loaded."
msgstr ""

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/certificate.py:0
msgid "The public key from the certificate could not be loaded."
msgstr ""

#. module: certificate
#: model:ir.model.fields,help:certificate.field_certificate_certificate__serial_number
msgid "The serial number to add to electronic documents"
msgstr ""

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/certificate.py:0
msgid ""
"This certificate could not be loaded. Either the content or the password is "
"erroneous."
msgstr ""

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/certificate.py:0
msgid "This certificate is not valid, its validity has expired."
msgstr ""

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/key.py:0
msgid ""
"This key could not be loaded. Either its content or its password is "
"erroneous."
msgstr ""

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/key.py:0
msgid ""
"Unsupported asymmetric cryptography algorithm '%s'. Currently supported for "
"decryption: RSA."
msgstr ""

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/key.py:0
msgid ""
"Unsupported asymmetric cryptography algorithm '%s'. Currently supported for "
"signature: EC and RSA."
msgstr ""

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/key.py:0
msgid ""
"Unsupported asymmetric cryptography algorithm '%s'. Currently supported: EC,"
" RSA."
msgstr ""

#. module: certificate
#: model:ir.model.fields,help:certificate.field_certificate_certificate__public_key_id
msgid ""
"Used to set a public key in case the one self-contained in the certificate is erroneus.\n"
"                When a public key is set this way, it will be used instead of the one in the certificate.\n"
"             "
msgstr ""

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__is_valid
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_search
msgid "Valid"
msgstr ""

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_search
msgid "Valid certificates"
msgstr ""

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_form
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_list
msgid "Validity"
msgstr ""

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_form
msgid "certificate form"
msgstr ""

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_list
msgid "certificate list"
msgstr ""

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_search
msgid "certificate search"
msgstr ""

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_form
msgid "e.g. New Certificate"
msgstr ""

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_key_view_form
msgid "key form"
msgstr ""

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_key_view_list
msgid "key list"
msgstr ""

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_key_view_search
msgid "key search"
msgstr ""
