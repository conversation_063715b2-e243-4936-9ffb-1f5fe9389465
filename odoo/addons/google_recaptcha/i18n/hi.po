# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* google_recaptcha
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-07 20:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Hindi (https://app.transifex.com/odoo/teams/41243/hi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: google_recaptcha
#: model_terms:ir.ui.view,arch_db:google_recaptcha.res_config_settings_view_form
msgid "<i class=\"oi oi-arrow-right\"/> Generate reCAPTCHA v3 keys"
msgstr ""

#. module: google_recaptcha
#: model:ir.model.fields,help:google_recaptcha.field_res_config_settings__recaptcha_min_score
msgid ""
"By default, should be one of 0.1, 0.3, 0.7, 0.9.\n"
"1.0 is very likely a good interaction, 0.0 is very likely a bot"
msgstr ""

#. module: google_recaptcha
#: model:ir.model,name:google_recaptcha.model_res_config_settings
msgid "Config Settings"
msgstr "कॉन्फ़िगरेशन सेटिंग"

#. module: google_recaptcha
#: model:ir.model,name:google_recaptcha.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: google_recaptcha
#: model:ir.model.fields,field_description:google_recaptcha.field_res_config_settings__recaptcha_min_score
msgid "Minimum score"
msgstr ""

#. module: google_recaptcha
#. odoo-javascript
#: code:addons/google_recaptcha/static/src/js/recaptcha.js:0
msgid "No recaptcha site key set."
msgstr ""

#. module: google_recaptcha
#. odoo-javascript
#: code:addons/google_recaptcha/static/src/xml/recaptcha.xml:0
msgid "Privacy Policy"
msgstr ""

#. module: google_recaptcha
#: model_terms:ir.ui.view,arch_db:google_recaptcha.res_config_settings_view_form
msgid ""
"Protect your forms from spam and abuse. If no keys are provided, no checks "
"will be done."
msgstr ""

#. module: google_recaptcha
#. odoo-javascript
#: code:addons/google_recaptcha/static/src/xml/recaptcha.xml:0
msgid "Protected by reCAPTCHA,"
msgstr ""

#. module: google_recaptcha
#: model:ir.model.fields,field_description:google_recaptcha.field_res_config_settings__recaptcha_private_key
msgid "Secret Key"
msgstr ""

#. module: google_recaptcha
#: model:ir.model.fields,field_description:google_recaptcha.field_res_config_settings__recaptcha_public_key
msgid "Site Key"
msgstr ""

#. module: google_recaptcha
#. odoo-javascript
#: code:addons/google_recaptcha/static/src/xml/recaptcha.xml:0
msgid "Terms of Service"
msgstr ""

#. module: google_recaptcha
#. odoo-python
#: code:addons/google_recaptcha/models/ir_http.py:0
msgid "The reCaptcha private key is invalid."
msgstr ""

#. module: google_recaptcha
#. odoo-python
#: code:addons/google_recaptcha/models/ir_http.py:0
msgid "The reCaptcha token is invalid."
msgstr ""

#. module: google_recaptcha
#. odoo-javascript
#: code:addons/google_recaptcha/static/src/js/recaptcha.js:0
msgid "The recaptcha site key is invalid."
msgstr ""

#. module: google_recaptcha
#. odoo-python
#: code:addons/google_recaptcha/models/ir_http.py:0
msgid "The request is invalid or malformed."
msgstr ""

#. module: google_recaptcha
#. odoo-python
#: code:addons/google_recaptcha/models/ir_http.py:0
msgid "Your request has timed out, please retry."
msgstr ""

#. module: google_recaptcha
#. odoo-javascript
#: code:addons/google_recaptcha/static/src/xml/recaptcha.xml:0
msgid "apply."
msgstr ""
