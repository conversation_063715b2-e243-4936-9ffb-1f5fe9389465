# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* google_recaptcha
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-07 20:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: google_recaptcha
#: model_terms:ir.ui.view,arch_db:google_recaptcha.res_config_settings_view_form
msgid "<i class=\"oi oi-arrow-right\"/> Generate reCAPTCHA v3 keys"
msgstr "<i class=\"oi oi-arrow-right\"/> Generiraj ključe reCAPTCHA v3"

#. module: google_recaptcha
#: model:ir.model.fields,help:google_recaptcha.field_res_config_settings__recaptcha_min_score
msgid ""
"By default, should be one of 0.1, 0.3, 0.7, 0.9.\n"
"1.0 is very likely a good interaction, 0.0 is very likely a bot"
msgstr ""
"Privzeto bi moral biti eden od 0.1, 0.3, 0.7, 0.9.\n"
"1.0 je zelo verjetno dobra interakcija, 0.0 je zelo verjetno bot"

#. module: google_recaptcha
#: model:ir.model,name:google_recaptcha.model_res_config_settings
msgid "Config Settings"
msgstr "Uredi nastavitve"

#. module: google_recaptcha
#: model:ir.model,name:google_recaptcha.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP usmerjanje"

#. module: google_recaptcha
#: model:ir.model.fields,field_description:google_recaptcha.field_res_config_settings__recaptcha_min_score
msgid "Minimum score"
msgstr "Najnižji rezultat"

#. module: google_recaptcha
#. odoo-javascript
#: code:addons/google_recaptcha/static/src/js/recaptcha.js:0
msgid "No recaptcha site key set."
msgstr ""

#. module: google_recaptcha
#. odoo-javascript
#: code:addons/google_recaptcha/static/src/xml/recaptcha.xml:0
msgid "Privacy Policy"
msgstr "Pravilnik o zasebnosti"

#. module: google_recaptcha
#: model_terms:ir.ui.view,arch_db:google_recaptcha.res_config_settings_view_form
msgid ""
"Protect your forms from spam and abuse. If no keys are provided, no checks "
"will be done."
msgstr ""
"Zaščitite svoje obrazce pred neželeno pošto in zlorabo. Če ne navedete "
"ključev, preverjanja ne bodo izvedena."

#. module: google_recaptcha
#. odoo-javascript
#: code:addons/google_recaptcha/static/src/xml/recaptcha.xml:0
msgid "Protected by reCAPTCHA,"
msgstr "Zaščiteno s strani reCAPTCHA,"

#. module: google_recaptcha
#: model:ir.model.fields,field_description:google_recaptcha.field_res_config_settings__recaptcha_private_key
msgid "Secret Key"
msgstr "Skrivni ključ"

#. module: google_recaptcha
#: model:ir.model.fields,field_description:google_recaptcha.field_res_config_settings__recaptcha_public_key
msgid "Site Key"
msgstr "Ključ spletnega mesta"

#. module: google_recaptcha
#. odoo-javascript
#: code:addons/google_recaptcha/static/src/xml/recaptcha.xml:0
msgid "Terms of Service"
msgstr "Pogoji storitve"

#. module: google_recaptcha
#. odoo-python
#: code:addons/google_recaptcha/models/ir_http.py:0
msgid "The reCaptcha private key is invalid."
msgstr "Zasebni ključ reCaptcha ni veljaven."

#. module: google_recaptcha
#. odoo-python
#: code:addons/google_recaptcha/models/ir_http.py:0
msgid "The reCaptcha token is invalid."
msgstr "Žeton reCaptcha ni veljaven."

#. module: google_recaptcha
#. odoo-javascript
#: code:addons/google_recaptcha/static/src/js/recaptcha.js:0
msgid "The recaptcha site key is invalid."
msgstr "Ključ spletnega mesta reCAPTCHA je neveljaven."

#. module: google_recaptcha
#. odoo-python
#: code:addons/google_recaptcha/models/ir_http.py:0
msgid "The request is invalid or malformed."
msgstr "Zahteva je neveljavna ali napačno oblikovana."

#. module: google_recaptcha
#. odoo-python
#: code:addons/google_recaptcha/models/ir_http.py:0
msgid "Your request has timed out, please retry."
msgstr "Vaša zahteva je potekla, poskusite znova."

#. module: google_recaptcha
#. odoo-javascript
#: code:addons/google_recaptcha/static/src/xml/recaptcha.xml:0
msgid "apply."
msgstr "uporabi."
