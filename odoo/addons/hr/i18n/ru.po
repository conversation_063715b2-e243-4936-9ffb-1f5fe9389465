# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON> <k<PERSON><PERSON><PERSON>@gmail.com>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2025
# <PERSON><PERSON>, 2025
# <PERSON><PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 20:36+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_company__hr_presence_control_email_amount
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_email_amount
msgid "# emails to send"
msgstr "# электронная почта для отправки"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_job.py:0
msgid "%s (copy)"
msgstr "%s (копия)"

#. module: hr
#: model:ir.actions.report,print_report_name:hr.hr_employee_print_badge
msgid "'Badge - %s' % (object.name).replace('/', '')"
msgstr "'Значок - %s' % (object.name).replace('/', '')"

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "1 Onsite Interview"
msgstr "1 Собеседование на месте"

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "1 Phone Call"
msgstr "1 Телефонный звонок"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "12 days / year, including <br>6 of your choice."
msgstr "12 дней в году, включая <br>6 по вашему выбору."

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "2 open days"
msgstr "2 дня открытых дверей"

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "4 Days after Interview"
msgstr "через 4 дня после собеседования"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid ""
"<b>Congratulations!</b> May I recommend you to setup an <a "
"href=\"%s\">onboarding plan?</a>"
msgstr ""
"<b>Поздравляю!</b> Могу предложить вам запустить <a href=\"%s\">процесс по "
"введению в должность?</a>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "<i class=\"fa fa-building-o\" role=\"img\" aria-label=\"Company\" title=\"Company\"/>"
msgstr "<i class=\"fa fa-building-o\" role=\"img\" aria-label=\"Company\" title=\"Company\"/>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
msgid "<i class=\"fa fa-fw me-2 fa-envelope text-primary\" title=\"Email\"/>"
msgstr "<i class=\"fa fa-fw me-2 fa-envelope text-primary\" title=\"Email\"/>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
msgid "<i class=\"fa fa-fw me-2 fa-phone text-primary\" title=\"Phone\"/>"
msgstr "<i class=\"fa fa-fw me-2 fa-phone text-primary\" title=\"Телефон\"/>"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "<small><b>READ</b></small>"
msgstr "<small><b>ЧИТАТЬ</b></small>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "<span class=\"flex-shrink-0 ml8 me-2\">IP Addresses</span>"
msgstr "<span class=\"flex-shrink-0 ml8 me-2\">IP-адреса</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "<span class=\"flex-shrink-0 ml8 me-2\">Sent Emails</span>"
msgstr "<span class=\"flex-shrink-0 ml8 me-2\">Отправленные письма</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid ""
"<span class=\"o_form_label o_hr_form_label cursor-default\">Close "
"Activities</span>"
msgstr ""
"<span class=\"o_form_label o_hr_form_label cursor-default\">Близкие "
"мероприятия</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid ""
"<span class=\"o_form_label o_hr_form_label cursor-default\">Detailed "
"Reason</span>"
msgstr ""
"<span class=\"o_form_label o_hr_form_label cursor-default\">Подробная "
"причина</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid "<span class=\"o_form_label o_hr_form_label cursor-default\">HR Info</span>"
msgstr ""
"<span class=\"o_form_label o_hr_form_label cursor-default\">HR Info</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Not Connected\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                    Не подключено\n"
"                                </span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "<span class=\"o_stat_text\">Connected Since</span>"
msgstr "<span class=\"o_stat_text\" >Включен с тех пор как</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form_smartbutton_inherited
msgid "<span class=\"o_stat_text\">Contacts</span>"
msgstr "<span class=\"o_stat_text\">Контакты</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_partner_view_form
msgid "<span class=\"o_stat_text\">Employee</span>"
msgstr "<span class=\"o_stat_text\">Сотрудник</span>"

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "<span class=\"text-muted small\">Days to get an Offer</span>"
msgstr "<span class=\"text-muted small\">Дни для получения предложения</span>"

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "<span class=\"text-muted small\">Process</span>"
msgstr "<span class=\"text-muted small\">Процесс</span>"

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "<span class=\"text-muted small\">Time to Answer</span>"
msgstr "<span class=\"text-muted small\">Время отвечать</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "<span>Reporting</span>"
msgstr "<span>Отчетность</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "<span>View</span>"
msgstr "<span>Посмотреть</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "<span>new Employees</span>"
msgstr "<span>новые сотрудники</span>"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "A full-time position <br>Attractive salary package."
msgstr "Полная занятость <br>Привлекательный пакет заработной платы."

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_employee_user_uniq
msgid "A user cannot be linked to multiple employees in the same company."
msgstr ""
"Пользователь не может быть связан с несколькими сотрудниками одной и той же "
"компании."

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_presence_state__absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_presence_state__absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_presence_state__absent
msgid "Absent"
msgstr "Нет"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Achieve monthly sales objectives"
msgstr "Достижение ежемесячных целей по продажам"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_needaction
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_needaction
#: model:ir.model.fields,field_description:hr.field_hr_job__message_needaction
msgid "Action Needed"
msgstr "Требуются действия"

#. module: hr
#: model_terms:digest.tip,tip_description:hr.digest_tip_hr_0
msgid ""
"Activate Remote Work to let Employees specify where they are working from."
msgstr ""
"Включите функцию «Удаленная работа», чтобы сотрудники могли указывать, "
"откуда они работают."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__active
#: model:ir.model.fields,field_description:hr.field_hr_employee__active
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__active
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__active
#: model:ir.model.fields,field_description:hr.field_hr_job__active
#: model:ir.model.fields,field_description:hr.field_hr_work_location__active
msgid "Active"
msgstr "Активный"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__activity_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_ids
msgid "Activities"
msgstr "Активность"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__activity_exception_decoration
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Оформление исключения активности"

#. module: hr
#: model:ir.model,name:hr.model_mail_activity_plan
#: model:ir.ui.menu,name:hr.menu_config_plan_plan
msgid "Activity Plan"
msgstr "План деятельности"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__activity_state
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_state
msgid "Activity State"
msgstr "Состояние активности"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__activity_type_icon
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_type_icon
msgid "Activity Type Icon"
msgstr "Значок типа активности"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_tree
msgid "Activity by"
msgstr "Деятельность по"

#. module: hr
#: model:ir.model,name:hr.model_mail_activity_plan_template
msgid "Activity plan template"
msgstr "Шаблон плана деятельности"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.mail_activity_plan_action
msgid ""
"Activity plans are used to assign a list of activities in just a few clicks\n"
"                    (e.g. \"Onboarding\", \"Offboarding\", ...)"
msgstr ""
"Планы дел позволяют назначать список дел в несколько кликов\n"
"                    (например, «Онбординг», «Оффбординг» и т. п.)"

#. module: hr
#: model:ir.model,name:hr.model_mail_activity_schedule
msgid "Activity schedule plan Wizard"
msgstr "План-график деятельности Мастер"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_employee_public_action
#: model_terms:ir.actions.act_window,help:hr.open_view_employee_list_my
msgid "Add a new employee"
msgstr "Добавить нового сотрудника"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__departure_description
#: model:ir.model.fields,field_description:hr.field_hr_employee__departure_description
msgid "Additional Information"
msgstr "Дополнительная информация"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid ""
"Additional Information: \n"
" %(description)s"
msgstr ""
"Дополнительная информация:\n"
" %(description)s"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__additional_note
#: model:ir.model.fields,field_description:hr.field_res_users__additional_note
msgid "Additional Note"
msgstr "Дополнительное примечание"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Additional languages"
msgstr "Дополнительные языки"

#. module: hr
#: model:hr.department,name:hr.dep_administration
msgid "Administration"
msgstr "Администрирование"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Administrative Work"
msgstr "Административная работа"

#. module: hr
#: model:res.groups,name:hr.group_hr_manager
msgid "Administrator"
msgstr "Администратор"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__module_hr_presence
msgid "Advanced Presence Control"
msgstr "Расширенный контроль присутствия"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Advanced presence of employees"
msgstr "Расширенное присутствие сотрудников"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_crm_team__alias_contact
#: model:ir.model.fields,field_description:hr.field_mail_alias__alias_contact
#: model:ir.model.fields,field_description:hr.field_mail_group__alias_contact
#: model:ir.model.fields,field_description:hr.field_maintenance_equipment_category__alias_contact
msgid "Alias Contact Security"
msgstr "Безопасность контакта с псевдонимом"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Allow employees to update their own data"
msgstr ""
"Предоставьте сотрудникам возможность самостоятельно обновлять свои данные"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Allow employees to update their own data."
msgstr "Позволить сотрудникам обновлять свои данные."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Application Settings"
msgstr "Настройки приложения"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid "Apply"
msgstr "Применить"

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_apprenticeship
msgid "Apprenticeship"
msgstr "Стажировка"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Approvers"
msgstr "Утвердители"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "Archive"
msgstr "Архив"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_archive
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_presence_state__archive
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_archive
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_presence_state__archive
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_archive
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_presence_state__archive
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
#: model_terms:ir.ui.view,arch_db:hr.view_department_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Archived"
msgstr "Архивировано"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid ""
"As an employee of our company, you will <b>collaborate with each department to create and deploy\n"
"                                disruptive products.</b> Come work at a growing company that offers great benefits with opportunities to\n"
"                                moving forward and learn alongside accomplished leaders. We're seeking an experienced and outstanding member of staff.\n"
"                                <br><br>\n"
"                                This position is both <b>creative and rigorous</b> by nature you need to think outside the box.\n"
"                                We expect the candidate to be proactive and have a \"get it done\" spirit. To be successful,\n"
"                                you will have solid solving problem skills."
msgstr ""
"Как сотрудник нашей компании, вы будете <b>взаимодействовать со всеми отделами для создания и внедрения\n"
"                                революционных продуктов.</b> Приходите работать в развивающуюся компанию с отличными льготами, условиями для карьерного роста и \n"
"                                возможностью обучения у ведущих лидеров. Мы ищем опытного и талантливого сотрудника.\n"
"                               <br><br>\n"
"                                Эта должность требует как <b>творческого подхода, так и строгость в выполнении задач</b>, поэтому мы ищем кандидата с нестандартным мышлением.\n"
"                                Мы ожидаем от кандидата проактивности и стремления довести начатое до конца. Успешный кандидат\n"
"                                должен обладать навыками решения сложных задач."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_mail_activity_plan_template__responsible_type
msgid "Assignment"
msgstr "Назначение"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_attachment_count
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_attachment_count
#: model:ir.model.fields,field_description:hr.field_hr_job__message_attachment_count
msgid "Attachment Count"
msgstr "Количество вложений"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Attendance"
msgstr "Посещаемость"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Attendance/Point of Sale"
msgstr "Посещаемость/Торговая точка"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__mail_alias__alias_contact__employees
msgid "Authenticated Employees"
msgstr "Аутентифицированные сотрудники"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.discuss_channel_view_form
msgid "Auto Subscribe Departments"
msgstr "Отделы автоподписки"

#. module: hr
#: model:ir.model.fields,help:hr.field_discuss_channel__subscription_department_ids
msgid "Automatically subscribe members of those departments to the channel."
msgstr "Автоматически подписывайте сотрудников этих отделов на канал."

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Autonomy"
msgstr "Автономия"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Available"
msgstr "Доступно"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.mail_activity_plan_view_form
msgid "Available for all Departments"
msgstr "Доступно для всех отделов"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_1920
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_1920
msgid "Avatar"
msgstr "Аватар"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_1024
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_1024
msgid "Avatar 1024"
msgstr "Аватар 1024"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_128
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_128
msgid "Avatar 128"
msgstr "Аватар 128"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_256
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_256
msgid "Avatar 256"
msgstr "Аватар 256"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_512
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_512
msgid "Avatar 512"
msgstr "Аватар 512"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Away"
msgstr "Нет на месте"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__bachelor
msgid "Bachelor"
msgstr "Бакалавр"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Bachelor Degree or Higher"
msgstr "Степень бакалавра или выше"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__barcode
#: model:ir.model.fields,field_description:hr.field_res_users__barcode
msgid "Badge ID"
msgstr "ID бейджа"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__bank_account_id
msgid "Bank Account"
msgstr "Расчётный счет"

#. module: hr
#: model:ir.model,name:hr.model_res_partner_bank
msgid "Bank Accounts"
msgstr "Банковские Счета"

#. module: hr
#: model:ir.model,name:hr.model_base
msgid "Base"
msgstr "База"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_company__hr_presence_control_ip
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_ip
msgid "Based on IP Address"
msgstr "На основе IP-адреса"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_company__hr_presence_control_attendance
#: model:ir.model.fields,field_description:hr.field_res_config_settings__module_hr_attendance
msgid "Based on attendances"
msgstr "На основе посещаемости"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_company__hr_presence_control_email
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_email
msgid "Based on number of emails sent"
msgstr "В зависимости от количества отправленных писем"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_company__hr_presence_control_login
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_login
msgid "Based on user status in system"
msgstr "На основе статуса пользователя в системе"

#. module: hr
#: model:ir.model,name:hr.model_hr_employee_base
msgid "Basic Employee"
msgstr "Основной сотрудник"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/background_image/background_image.xml:0
msgid "Binary file"
msgstr "Бинарный файл"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__can_edit
msgid "Can Edit"
msgstr "Можно редактировать"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_users_simple_form
msgid "Cancel"
msgstr "Отменить"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__employee_type
#: model:ir.model.fields,help:hr.field_res_users__employee_type
msgid ""
"Categorize your Employees by type. This field also has an impact on "
"contracts. Only Employees, Students and Trainee will have contract history."
msgstr ""
"Классифицируйте сотрудников по типу. Это поле влияет на договоры. Только "
"сотрудники, студенты и стажеры будут иметь историю договоров."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__certificate
#: model:ir.model.fields,field_description:hr.field_res_users__certificate
msgid "Certificate Level"
msgstr "Уровень сертификата"

#. module: hr
#: model:ir.actions.act_window,name:hr.res_users_action_my
msgid "Change my Preferences"
msgstr "Изменить мои предпочтения"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/employee_chat/employee_chat.xml:0
msgid "Chat"
msgstr "Чат"

#. module: hr
#: model:hr.job,name:hr.job_ceo
msgid "Chief Executive Officer"
msgstr "Генеральный директор"

#. module: hr
#: model:hr.job,name:hr.job_cto
msgid "Chief Technical Officer"
msgstr "Главный технический специалист"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__child_ids
msgid "Child Departments"
msgstr "Детские отделы"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "Child departments"
msgstr "Дочерние отделы"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Citizenship"
msgstr "Гражданство"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "City"
msgstr "Город"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__coach_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__coach_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__coach_id
#: model:ir.model.fields,field_description:hr.field_res_users__coach_id
#: model:ir.model.fields.selection,name:hr.selection__mail_activity_plan_template__responsible_type__coach
msgid "Coach"
msgstr "Тренер"

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan_template.py:0
msgid "Coach of employee %s is not set."
msgstr "Тренер сотрудника %s не установлен."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__code
msgid "Code"
msgstr "Код"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_department_form
#: model_terms:ir.ui.view,arch_db:hr.view_department_tree
msgid "Color"
msgstr "Цвет"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__color
#: model:ir.model.fields,field_description:hr.field_hr_employee__color
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__color
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__color
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__color
msgid "Color Index"
msgstr "Цветовой индекс"

#. module: hr
#: model:ir.model,name:hr.model_res_company
#: model_terms:ir.ui.view,arch_db:hr.view_department_tree
msgid "Companies"
msgstr "Компании"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__company_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__company_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__company_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__company_id
#: model:ir.model.fields,field_description:hr.field_hr_job__company_id
#: model:ir.model.fields,field_description:hr.field_hr_work_location__company_id
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Company"
msgstr "Компания"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__company_country_id
msgid "Company Country"
msgstr "Страна компании"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.print_employee_badge
msgid "Company Logo"
msgstr "Логотип компании"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__resource_calendar_id
msgid "Company Working Hours"
msgstr "Часы работы компании"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_id
msgid "Company employee"
msgstr "Сотрудник компании"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__complete_name
msgid "Complete Name"
msgstr "Полное имя"

#. module: hr
#: model:ir.model,name:hr.model_res_config_settings
msgid "Config Settings"
msgstr "Параметры конфигурации"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_human_resources_configuration
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "Configuration"
msgstr "Конфигурация"

#. module: hr
#: model:hr.job,name:hr.job_consultant
msgid "Consultant"
msgstr "Консультант"

#. module: hr
#: model:ir.model,name:hr.model_res_partner
msgid "Contact"
msgstr "Контакты"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Contact Information"
msgstr "Контактная информация"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__emergency_contact
#: model:ir.model.fields,field_description:hr.field_res_users__emergency_contact
msgid "Contact Name"
msgstr "Контактное лицо"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__emergency_phone
#: model:ir.model.fields,field_description:hr.field_res_users__emergency_phone
msgid "Contact Phone"
msgstr "Контактный телефон"

#. module: hr
#: model:ir.model,name:hr.model_hr_contract_type
msgid "Contract Type"
msgstr "Тип контракта"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_contract_type_view_tree
msgid "Contract Types"
msgstr "Типы договоров"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__contractor
msgid "Contractor"
msgstr "Подрядчик"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__country_id
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Country"
msgstr "Страна"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__company_country_code
msgid "Country Code"
msgstr "Код страны"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__country_of_birth
#: model:ir.model.fields,field_description:hr.field_res_users__country_of_birth
msgid "Country of Birth"
msgstr "Страна рождения"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_work_location__location_type
msgid "Cover Image"
msgstr "Изображение обложки"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__create_date
msgid "Create Date"
msgstr "Дата создания"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_users_simple_form_inherit_hr
msgid "Create Employee"
msgstr "Создать Сотрудника"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Create User"
msgstr "Создать пользователя"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_department_kanban_action
#: model_terms:ir.actions.act_window,help:hr.hr_department_tree_action
msgid "Create a new department"
msgstr "Создать новое подразделение"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_contract_type_action
msgid "Create a new employment type"
msgstr "Создайте новый тип занятости"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_work_location_action
msgid "Create a new work location"
msgstr "Создайте новое рабочее место"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.mail_activity_plan_action
msgid "Create an Activity Plan"
msgstr "Создать план задач"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Create content that will help our users on a daily basis"
msgstr ""
"Создавать контент, который будет ежедневно помогать нашим пользователям"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form
msgid "Create employee"
msgstr "Создание сотрудника"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_department__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_job__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_work_location__create_uid
msgid "Created by"
msgstr "Создано"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__create_date
#: model:ir.model.fields,field_description:hr.field_hr_department__create_date
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__create_date
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__create_date
#: model:ir.model.fields,field_description:hr.field_hr_employee__create_date
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__create_date
#: model:ir.model.fields,field_description:hr.field_hr_job__create_date
#: model:ir.model.fields,field_description:hr.field_hr_work_location__create_date
msgid "Created on"
msgstr "Создано"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__currency_id
msgid "Currency"
msgstr "Валюта"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__no_of_employee
msgid "Current Number of Employees"
msgstr "Текущее количество сотрудников"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Customer Relationship"
msgstr "Отношения с клиентами"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__birthday
#: model:ir.model.fields,field_description:hr.field_res_users__birthday
msgid "Date of Birth"
msgstr "Дата рождения"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_departure_reason.py:0
msgid "Default departure reasons cannot be deleted."
msgstr "Причины отъезда по умолчанию не могут быть удалены."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid ""
"Define the allowed IP to be displayed as Present. In case of multiple "
"addresses, separate them by a coma."
msgstr ""
"Укажите допустимые IP‑адреса, при которых сотрудник считается «На месте». "
"Несколько адресов разделяйте запятой."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Define the minimum number of sent emails to be displayed as Present."
msgstr ""
"Укажите минимальное число отправленных писем, чтобы статус отображался как "
"«На месте»."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__resource_calendar_id
#: model:ir.model.fields,help:hr.field_res_users__employee_resource_calendar_id
msgid ""
"Define the working schedule of the resource. If not set, the resource will "
"have fully flexible working hours."
msgstr ""
"Задайте график работы ресурса. Если не задан, график будет полностью гибким."

#. module: hr
#: model_terms:hr.job,description:hr.job_ceo
msgid ""
"Demonstration of different Odoo services for each client and convincing the client about functionality of the application.\n"
"The candidate should have excellent communication skills.\n"
"Relationship building and influencing skills\n"
"Expertise in New Client Acquisition (NCAs) and Relationship Management.\n"
"Gathering market and customer information.\n"
"Coordinating with the sales and support team for adopting different strategies\n"
"Reviewing progress and identifying opportunities and new areas for development.\n"
"Building strong relationships with clients / customers for business growth profitability.\n"
"Keep regular interaction with key clients for better extraction and expansion."
msgstr ""
"Демонстрация различных сервисов Odoo для каждого клиента и убеждение клиента в функциональности модуля.\n"
"Кандидат должен обладать:\n"
"Отличными коммуникативными навыками;\n"
"Навыками построения отношений и оказания влияния;\n"
"Опытом в привлечении новых клиентов и управлении взаимоотношениями;\n"
"Навыками сбора информации о рынке и клиентах;\n"
"Задачи:\n"
"Координация действий с отделом продаж и службой поддержки для принятия различных стратегий.\n"
"Анализ прогресса и выявление возможностей и новых областей для развития.\n"
"Выстраивание прочных отношений с клиентами/заказчиками для обеспечения роста рентабельности бизнеса.\n"
"Поддержание регулярного взаимодействия с ключевыми клиентами для более эффективного извлечения прибыли и расширения бизнеса."

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/avatar_card/avatar_card_popover_patch.xml:0
#: code:addons/hr/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
#: model:ir.model,name:hr.model_hr_department
#: model:ir.model.fields,field_description:hr.field_hr_employee__department_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__department_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__department_id
#: model:ir.model.fields,field_description:hr.field_hr_job__department_id
#: model:ir.model.fields,field_description:hr.field_mail_activity_plan__department_id
#: model:ir.model.fields,field_description:hr.field_mail_activity_schedule__department_id
#: model:ir.model.fields,field_description:hr.field_res_users__department_id
#: model:ir.model.fields,field_description:hr.field_resource_resource__department_id
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Department"
msgstr "Отдел"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_mail_activity_plan__department_assignable
msgid "Department Assignable"
msgstr "Можно назначить отдел"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__name
msgid "Department Name"
msgstr "Название отдела"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/department_chart/department_chart.xml:0
msgid "Department Organization"
msgstr "Организация департамента"

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_department_kanban_action
#: model:ir.actions.act_window,name:hr.hr_department_tree_action
#: model:ir.ui.menu,name:hr.menu_hr_department_kanban
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
msgid "Departments"
msgstr "Отделения"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Departure"
msgstr "Отъезд"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__departure_date
#: model:ir.model.fields,field_description:hr.field_hr_employee__departure_date
msgid "Departure Date"
msgstr "Дата отъезда"

#. module: hr
#: model:ir.model,name:hr.model_hr_departure_reason
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__departure_reason_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__departure_reason_id
msgid "Departure Reason"
msgstr "Причина отъезда"

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_departure_reason_action
#: model:ir.ui.menu,name:hr.menu_hr_departure_reason_tree
msgid "Departure Reasons"
msgstr "Причины отъезда"

#. module: hr
#: model:ir.model,name:hr.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "Мастер Ухода"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Dependant"
msgstr "Зависимый"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__child_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__child_ids
msgid "Direct subordinates"
msgstr "Прямые подчиненные"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_hr_employee
msgid "Directory"
msgstr "Каталог"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid "Discard"
msgstr "Отменить"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Discover our products."
msgstr "Откройте для себя нашу продукцию."

#. module: hr
#: model:ir.model,name:hr.model_discuss_channel
msgid "Discussion Channel"
msgstr "Дискуссионный канал"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__display_name
#: model:ir.model.fields,field_description:hr.field_hr_department__display_name
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__display_name
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__display_name
#: model:ir.model.fields,field_description:hr.field_hr_employee__display_name
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__display_name
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__display_name
#: model:ir.model.fields,field_description:hr.field_hr_job__display_name
#: model:ir.model.fields,field_description:hr.field_hr_work_location__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid ""
"Display remote work settings for each employee and dedicated reports. "
"Presence icons will be updated with remote work location."
msgstr ""
"Отображение настроек удаленной работы для каждого сотрудника и специальные "
"отчеты. Значки присутствия будут обновляться с учетом местоположения "
"удаленной работы."

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "Divorced"
msgstr "В разводе"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__doctor
msgid "Doctor"
msgstr "Врач"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__driving_license
msgid "Driving License"
msgstr "Водительское удостоверение"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid ""
"Each employee has a chance to see the impact of his work.\n"
"                            You can make a real contribution to the success of the company.\n"
"                            <br>\n"
"                            Several activities are often organized all over the year, such as weekly\n"
"                            sports sessions, team building events, monthly drink, and much more."
msgstr ""
"Каждый сотрудник может увидеть результаты своей работы и внести реальный вклад в успех компании. \n"
"                            <br>\n"
"                            В течение года регулярно проводятся различные мероприятия  –  еженедельные\n"
"                            спортивные занятия, тимбилдинги, ежемесячные встречи и др."

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Eat &amp; Drink"
msgstr "Еда и напитки"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Education"
msgstr "Образование"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__email
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__email
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__email
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Email"
msgstr "Email"

#. module: hr
#: model:ir.model,name:hr.model_mail_alias
msgid "Email Aliases"
msgstr "Псевдонимы электронной почты"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Emergency"
msgstr "Аварийный"

#. module: hr
#. odoo-python
#: code:addons/hr/models/res_partner.py:0 code:addons/hr/models/res_users.py:0
#: model:hr.contract.type,name:hr.contract_type_employee
#: model:ir.model,name:hr.model_hr_employee
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__employee_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__employee_id
#: model:ir.model.fields,field_description:hr.field_hr_manager_department_report__employee_id
#: model:ir.model.fields,field_description:hr.field_resource_resource__employee_id
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__employee
#: model:ir.model.fields.selection,name:hr.selection__mail_activity_plan_template__responsible_type__employee
#: model:ir.ui.menu,name:hr.menu_config_employee
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
#: model_terms:ir.ui.view,arch_db:hr.view_users_simple_form_inherit_hr
msgid "Employee"
msgstr "Сотрудник"

#. module: hr
#: model:ir.model,name:hr.model_hr_employee_category
msgid "Employee Category"
msgstr "Категория сотрудников"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_count
msgid "Employee Count"
msgstr "Количество сотрудников"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_employee_self_edit
msgid "Employee Editing"
msgstr "Редактирование сотрудников"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.print_employee_badge
msgid "Employee Image"
msgstr "Изображение сотрудника"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__private_lang
msgid "Employee Lang"
msgstr "Сотрудник Лэнг"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__name
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
msgid "Employee Name"
msgstr "ФИО сотрудника"

#. module: hr
#: model:ir.actions.act_window,name:hr.mail_activity_plan_action
msgid "Employee Plans"
msgstr "Планы для сотрудников"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_company__employee_properties_definition
msgid "Employee Properties"
msgstr "Свойства сотрудников"

#. module: hr
#: model:ir.actions.act_window,name:hr.open_view_categ_form
#: model:ir.model.fields,field_description:hr.field_res_users__category_ids
#: model_terms:ir.ui.view,arch_db:hr.view_employee_category_form
msgid "Employee Tags"
msgstr "Метки сотрудников"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/views/archive_employee_hook.js:0
msgid "Employee Termination"
msgstr "Увольнение сотрудника"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__employee_type
#: model:ir.model.fields,field_description:hr.field_res_users__employee_type
msgid "Employee Type"
msgstr "Тип сотрудника"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Employee Update Rights"
msgstr "Права сотрудников на обновление информации"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__bank_account_id
#: model:ir.model.fields,help:hr.field_res_users__employee_bank_account_id
msgid "Employee bank account to pay salaries"
msgstr "Банковский счет сотрудника для выплаты зарплаты"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_bank_account_id
msgid "Employee's Bank Account Number"
msgstr "Номер банковского счета сотрудника"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_country_id
msgid "Employee's Country"
msgstr "Страна сотрудника"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Employee's Name"
msgstr "Имя сотрудника"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_resource_calendar_id
msgid "Employee's Working Hours"
msgstr "Рабочее время сотрудника"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_department.py:0
#: model:ir.actions.act_window,name:hr.hr_employee_public_action
#: model:ir.actions.act_window,name:hr.open_view_employee_list
#: model:ir.actions.act_window,name:hr.open_view_employee_list_my
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__employee_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__employee_ids
#: model:ir.model.fields,field_description:hr.field_res_partner__employee_ids
#: model:ir.ui.menu,name:hr.menu_hr_employee_payroll
#: model:ir.ui.menu,name:hr.menu_hr_employee_user
#: model:ir.ui.menu,name:hr.menu_hr_root
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_tree
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_view_activity
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr.res_partner_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_department_form
#: model_terms:ir.ui.view,arch_db:hr.view_department_tree
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_tree
msgid "Employees"
msgstr "Сотрудники"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_partner__employees_count
#: model:ir.model.fields,field_description:hr.field_res_users__employees_count
msgid "Employees Count"
msgstr "Количество сотрудников"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_category_list
msgid "Employees Tags"
msgstr "Теги Сотрудники"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__contract_type_id
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Employment Type"
msgstr "Тип занятости"

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_contract_type_action
#: model:ir.ui.menu,name:hr.menu_view_hr_contract_type
msgid "Employment Types"
msgstr "Виды занятости"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Enrich employee profiles with skills and resumes"
msgstr "Дополните профили сотрудников навыками и резюме"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Expand your knowledge of various business industries"
msgstr "Расширьте свои знания о различных отраслях бизнеса"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job__expected_employees
msgid ""
"Expected number of employees for this job position after new recruitment."
msgstr ""
"Планируемое количество сотрудников на данной должности по окончании нового "
"набора сотрудников."

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Experience in writing online content"
msgstr "Опыт написания онлайн-контента"

#. module: hr
#: model:hr.job,name:hr.job_developer
msgid "Experienced Developer"
msgstr "Опытный разработчик"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__share
#: model:ir.model.fields,help:hr.field_hr_employee_base__share
#: model:ir.model.fields,help:hr.field_hr_employee_public__share
msgid ""
"External user with limited access, created only for the purpose of sharing "
"data."
msgstr ""
"Внешний пользователь с ограниченным доступом, создан для обмена данными."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Family Status"
msgstr "Семейное положение"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__gender__female
msgid "Female"
msgstr "Женский"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__study_field
#: model:ir.model.fields,field_description:hr.field_res_users__study_field
msgid "Field of Study"
msgstr "Область изучения"

#. module: hr
#: model:hr.departure.reason,name:hr.departure_fired
msgid "Fired"
msgstr "Уволен"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_follower_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_follower_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__message_follower_ids
msgid "Followers"
msgstr "Подписчики"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_partner_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_partner_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__message_partner_ids
msgid "Followers (Partners)"
msgstr "Подписчики"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__activity_type_icon
#: model:ir.model.fields,help:hr.field_hr_employee__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Шрифт, отличный значок, например. к.-а.-задачи"

#. module: hr
#. odoo-python
#: code:addons/hr/models/discuss_channel.py:0
msgid ""
"For %(channels)s, channel_type should be 'channel' to have the department "
"auto-subscription."
msgstr ""
"Для %(channels)s тип_канала должен быть 'channel', чтобы отдел имел "
"автоподписку."

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__freelance
msgid "Freelancer"
msgstr "Исполнитель"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Fruit, coffee and <br>snacks provided."
msgstr "Предоставляются фрукты, кофе и закуски <br>."

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_full_time
msgid "Full-Time"
msgstr "Постоянно"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Future Activities"
msgstr "Планируемые действия"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__gender
#: model:ir.model.fields,field_description:hr.field_res_users__gender
msgid "Gender"
msgstr "Пол"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Generate"
msgstr "Генерировать"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid "Give more details about the reason of archiving the employee."
msgstr "Укажите более подробную информацию о причине архивации сотрудника."

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Google Adwords experience"
msgstr "Опыт работы с Google Adwords"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__graduate
msgid "Graduate"
msgstr "Выпускник"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Great team of smart people, in a friendly and open culture"
msgstr "Отличная команда умных людей c дружелюбной и открытой культурой"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Group By"
msgstr "Группировать по"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_discuss_channel__subscription_department_ids
msgid "HR Departments"
msgstr "Отдел кадров"

#. module: hr
#: model:ir.actions.server,name:hr.ir_cron_data_check_work_permit_validity_ir_actions_server
msgid "HR Employee: check work permit validity"
msgstr "Сотрудник HR: проверьте действительность разрешения на работу"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/hr_presence_status/hr_presence_status.js:0
msgid "HR Presence Status"
msgstr "Статус присутствия HR"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "HR Settings"
msgstr "Кадровые данные"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_manager_department_report__has_department_manager_access
msgid "Has Department Manager Access"
msgstr "Имеет права руководителя отдела"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__has_message
#: model:ir.model.fields,field_description:hr.field_hr_employee__has_message
#: model:ir.model.fields,field_description:hr.field_hr_job__has_message
msgid "Has Message"
msgstr "Есть сообщение"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Highly creative and autonomous"
msgstr "Высокая креативность и самостоятельность"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__work_location_type__home
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__work_location_type__home
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__work_location_type__home
#: model:ir.model.fields.selection,name:hr.selection__hr_work_location__location_type__home
msgid "Home"
msgstr "Главная"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__distance_home_work
#: model:ir.model.fields,field_description:hr.field_res_users__distance_home_work
msgid "Home-Work Distance"
msgstr "Расстояние до дома"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__km_home_work
#: model:ir.model.fields,field_description:hr.field_res_users__km_home_work
msgid "Home-Work Distance in Km"
msgstr "Расстояние дом‑работа в км"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__distance_home_work_unit
#: model:ir.model.fields,field_description:hr.field_res_users__distance_home_work_unit
msgid "Home-Work Distance unit"
msgstr "Единица измерения расстояния дом‑работа"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__hr_icon_display
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__hr_icon_display
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__hr_icon_display
#: model:ir.model.fields,field_description:hr.field_resource_resource__hr_icon_display
msgid "Hr Icon Display"
msgstr "Отображение значков часов"

#. module: hr
#: model:ir.model,name:hr.model_hr_manager_department_report
msgid "Hr Manager Department Report"
msgstr "Отчет руководителя отдела кадров"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__hr_presence_state
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__hr_presence_state
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__hr_presence_state
#: model:ir.model.fields,field_description:hr.field_res_users__hr_presence_state
msgid "Hr Presence State"
msgstr "Hr Presence State"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_hr_main
msgid "Human Resources"
msgstr "HR"

#. module: hr
#: model:hr.job,name:hr.job_hrm
msgid "Human Resources Manager"
msgstr "Менеджер по персоналу"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__id
#: model:ir.model.fields,field_description:hr.field_hr_department__id
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__id
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__id
#: model:ir.model.fields,field_description:hr.field_hr_employee__id
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__id
#: model:ir.model.fields,field_description:hr.field_hr_job__id
#: model:ir.model.fields,field_description:hr.field_hr_work_location__id
msgid "ID"
msgstr "ID"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__id_card
msgid "ID Card Copy"
msgstr "Копия удостоверения личности"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__barcode
#: model:ir.model.fields,help:hr.field_res_users__barcode
msgid "ID used for employee identification."
msgstr "ID, используемый для идентификации сотрудника."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__im_status
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__im_status
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__im_status
msgid "IM Status"
msgstr "Статус IM"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__activity_exception_icon
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_exception_icon
msgid "Icon"
msgstr "Иконка"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__activity_exception_icon
#: model:ir.model.fields,help:hr.field_hr_employee__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Значок, обозначающий исключение Дела."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__identification_id
#: model:ir.model.fields,field_description:hr.field_res_users__identification_id
msgid "Identification No"
msgstr "Идентификация Нет"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_needaction
#: model:ir.model.fields,help:hr.field_hr_employee__message_needaction
#: model:ir.model.fields,help:hr.field_hr_job__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"Если флажок установлен, значит, новые сообщения требуют вашего внимания."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_has_error
#: model:ir.model.fields,help:hr.field_hr_department__message_has_sms_error
#: model:ir.model.fields,help:hr.field_hr_employee__message_has_error
#: model:ir.model.fields,help:hr.field_hr_employee__message_has_sms_error
#: model:ir.model.fields,help:hr.field_hr_job__message_has_error
#: model:ir.model.fields,help:hr.field_hr_job__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Если отмечено, некоторые сообщения имеют ошибку доставки."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Если для активного поля установлено значение False, это позволит вам скрыть "
"запись ресурса, не удаляя ее."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__private_car_plate
msgid "If you have more than one car, just separate the plates by a space."
msgstr "Если у вас несколько автомобилей, просто разделите номера пробелом."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_1920
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_1920
msgid "Image"
msgstr "Изображение"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_1024
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_1024
msgid "Image 1024"
msgstr "Изображение 1024"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_128
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_128
msgid "Image 128"
msgstr "Изображение 128"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_256
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_256
msgid "Image 256"
msgstr "Изображение 256"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_512
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_512
msgid "Image 512"
msgstr "Изображение 512"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "Import Template for Employees"
msgstr "Шаблон импорта для сотрудников"

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_interim
msgid "Interim"
msgstr "Переход"

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_part_time
msgid "Intern"
msgstr "Стажер"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__is_flexible
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__is_flexible
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__is_flexible
msgid "Is Flexible"
msgstr "Является гибким"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_is_follower
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_is_follower
#: model:ir.model.fields,field_description:hr.field_hr_job__message_is_follower
msgid "Is Follower"
msgstr "Является подписчиком"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__is_fully_flexible
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__is_fully_flexible
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__is_fully_flexible
msgid "Is Fully Flexible"
msgstr "Является полностью гибким"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__is_manager
msgid "Is Manager"
msgstr "Менеджер"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__is_system
msgid "Is System"
msgstr "Система"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_tree
msgid "Job"
msgstr "Работа"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__description
msgid "Job Description"
msgstr "Описание вакансии"

#. module: hr
#: model:ir.model,name:hr.model_hr_job
#: model:ir.model.fields,field_description:hr.field_hr_employee__job_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__job_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__job_id
#: model:ir.model.fields,field_description:hr.field_hr_job__name
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Job Position"
msgstr "Должность"

#. module: hr
#: model:ir.actions.act_window,name:hr.action_hr_job
#: model:ir.ui.menu,name:hr.menu_view_hr_job
msgid "Job Positions"
msgstr "Положения вакансии"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "Job Summary"
msgstr "Краткое описание вакансии"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__job_title
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__job_title
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__job_title
#: model:ir.model.fields,field_description:hr.field_res_users__job_title
#: model:ir.model.fields,field_description:hr.field_resource_resource__job_title
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Job Title"
msgstr "Название работы"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__jobs_ids
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Jobs"
msgstr "Вакансии"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__lang
msgid "Lang"
msgstr "Язык"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Language"
msgstr "Язык"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__last_activity
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__last_activity
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__last_activity
#: model:ir.model.fields,field_description:hr.field_res_users__last_activity
msgid "Last Activity"
msgstr "Последняя активность"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__last_activity_time
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__last_activity_time
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__last_activity_time
#: model:ir.model.fields,field_description:hr.field_res_users__last_activity_time
msgid "Last Activity Time"
msgstr "Время последней активности"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_department__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_job__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_work_location__write_uid
msgid "Last Updated by"
msgstr "Последнее обновление"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__write_date
#: model:ir.model.fields,field_description:hr.field_hr_department__write_date
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__write_date
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__write_date
#: model:ir.model.fields,field_description:hr.field_hr_employee__write_date
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__write_date
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__write_date
#: model:ir.model.fields,field_description:hr.field_hr_job__write_date
#: model:ir.model.fields,field_description:hr.field_hr_work_location__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Late Activities"
msgstr "Поздние Мероприятия"

#. module: hr
#: model:ir.actions.act_window,name:hr.plan_wizard_action
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_tree
msgid "Launch Plan"
msgstr "План запуска"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Lead the entire sales cycle"
msgstr "Руководите всем циклом продаж"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "Legal Cohabitant"
msgstr "Законный сожитель"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.action_hr_job
msgid "Let's create a job position."
msgstr "Давайте создадим вакансию."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Location"
msgstr "Местоположение"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_work_location__location_number
msgid "Location Number"
msgstr "код месторасположения "

#. module: hr
#: model:hr.department,name:hr.dep_rd_ltp
msgid "Long Term Projects"
msgstr "Долгосрочные проекты"

#. module: hr
#: model_terms:hr.job,description:hr.job_hrm
msgid ""
"Lorem Ipsum is simply dummy text of the printing and typesetting industry. "
"Lorem Ipsum has been the industry's standard dummy text ever since the "
"1500s, when an unknown printer took a galley of type and scrambled it to "
"make a type specimen book. It has survived not only five centuries, but also"
" the leap into electronic typesetting, remaining essentially unchanged. It "
"was popularised in the 1960s with the release of Letraset sheets containing "
"Lorem Ipsum passages, and more recently with desktop publishing software "
"like Aldus PageMaker including versions of Lorem Ipsum."
msgstr ""
"Lorem Ipsum is simply dummy text of the printing and typesetting industry. "
"Lorem Ipsum has been the industry's standard dummy text ever since the "
"1500s, when an unknown printer took a galley of type and scrambled it to "
"make a type specimen book. It has survived not only five centuries, but also"
" the leap into electronic typesetting, remaining essentially unchanged. It "
"was popularised in the 1960s with the release of Letraset sheets containing "
"Lorem Ipsum passages, and more recently with desktop publishing software "
"like Aldus PageMaker including versions of Lorem Ipsum."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_main_attachment_id
msgid "Main Attachment"
msgstr "Основное вложение"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__gender__male
msgid "Male"
msgstr "Мужской"

#. module: hr
#: model:hr.department,name:hr.dep_management
msgid "Management"
msgstr "Управление"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__manager_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__parent_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__parent_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__parent_id
#: model:ir.model.fields,field_description:hr.field_res_users__employee_parent_id
#: model:ir.model.fields.selection,name:hr.selection__mail_activity_plan_template__responsible_type__manager
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Manager"
msgstr "Руководитель"

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan_template.py:0
msgid "Manager of employee %s is not set."
msgstr "Менеджер сотрудника %s не установлен."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__marital
#: model:ir.model.fields,field_description:hr.field_res_users__marital
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Marital Status"
msgstr "Cемейное положение"

#. module: hr
#: model:hr.job,name:hr.job_marketing
msgid "Marketing and Community Manager"
msgstr "Менеджер по маркетингу и работе с населением"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "Married"
msgstr "В браке"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__master
msgid "Master"
msgstr "Мастер"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__master_department_id
msgid "Master Department"
msgstr "Отдел магистратуры"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Master demos of our software"
msgstr "Мастер-демонстрации нашего программного обеспечения"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__member_of_department
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__member_of_department
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__member_of_department
msgid "Member of department"
msgstr "Член департамента"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__member_ids
msgid "Members"
msgstr "Участники"

#. module: hr
#: model:ir.model,name:hr.model_ir_ui_menu
msgid "Menu"
msgstr "Меню"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_has_error
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_has_error
#: model:ir.model.fields,field_description:hr.field_hr_job__message_has_error
msgid "Message Delivery error"
msgstr "Ошибка доставки сообщения"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__message_ids
msgid "Messages"
msgstr "Сообщения"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Must Have"
msgstr "Должен быть"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__my_activity_date_deadline
#: model:ir.model.fields,field_description:hr.field_hr_employee__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Крайний срок моей активности"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "My Department"
msgstr "Мой отдел"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/user_menu/my_profile.js:0
msgid "My Profile"
msgstr "Мой профиль"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "My Team"
msgstr "Моя команда"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__name
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__name
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__name
msgid "Name"
msgstr "Имя"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__country_id
msgid "Nationality (Country)"
msgstr "Национальность"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Negotiate and contract"
msgstr "Вести переговоры и заключать контракты"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_view_graph
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_view_pivot
msgid "New Employees Over Time"
msgstr "Новые сотрудники с течением времени"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__newly_hired
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__newly_hired
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__newly_hired
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Newly Hired"
msgstr "Недавно принятые на работу"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__activity_calendar_event_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Следующее событие календаря активности"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__activity_date_deadline
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Следующий срок мероприятия"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__activity_summary
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_summary
msgid "Next Activity Summary"
msgstr "Резюме следующего действия"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__activity_type_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_type_id
msgid "Next Activity Type"
msgstr "Следующий Тип Мероприятия"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Nice to have"
msgstr "Приятно иметь"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/views/hr_graph_controller.xml:0
#: code:addons/hr/static/src/views/hr_pivot_controller.xml:0
msgid "No Data"
msgstr "Нет данных"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.open_view_categ_form
msgid "No Tags found ! Let's create one"
msgstr "Теги не найдены! Давайте создадим один"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "No dumb managers, no stupid tools to use, no rigid working hours"
msgstr ""
"Никаких глупых менеджеров, бестолковых инструментов, жесткого рабочего "
"графика"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid ""
"No waste of time in enterprise processes, real responsibilities and autonomy"
msgstr ""
"Отсутствие траты времени на корпоративные процессы, только реальные "
"обязанности и автономия"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Not available"
msgstr "Недоступно"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__note
msgid "Note"
msgstr "Заметка"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__notes
msgid "Notes"
msgstr "Заметки"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_needaction_counter
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_needaction_counter
#: model:ir.model.fields,field_description:hr.field_hr_job__message_needaction_counter
msgid "Number of Actions"
msgstr "Число действий"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__children
#: model:ir.model.fields,field_description:hr.field_res_users__children
msgid "Number of Dependent Children"
msgstr "Количество детей на иждивении"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_tree
msgid "Number of Employees"
msgstr "Количество сотрудников"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job__no_of_employee
msgid "Number of employees currently occupying this job position."
msgstr "Количество сотрудников, занимающих эту должность в настоящее время."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_has_error_counter
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_has_error_counter
#: model:ir.model.fields,field_description:hr.field_hr_job__message_has_error_counter
msgid "Number of errors"
msgstr "Число ошибок"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_needaction_counter
#: model:ir.model.fields,help:hr.field_hr_employee__message_needaction_counter
#: model:ir.model.fields,help:hr.field_hr_job__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Количество сообщений, требующих принятия мер"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_has_error_counter
#: model:ir.model.fields,help:hr.field_hr_employee__message_has_error_counter
#: model:ir.model.fields,help:hr.field_hr_job__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Количество недоставленных сообщений"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job__no_of_recruitment
msgid "Number of new employees you expect to recruit."
msgstr "Количество новых сотрудников, которых вы планируете нанять."

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__work_location_type__office
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__work_location_type__office
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__work_location_type__office
#: model:ir.model.fields.selection,name:hr.selection__hr_work_location__location_type__office
msgid "Office"
msgstr "Офис"

#. module: hr
#: model:res.groups,name:hr.group_hr_user
msgid "Officer: Manage all employees"
msgstr "Офицер: Управление всеми сотрудниками"

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan_template.py:0
msgid ""
"Oops! It seems there is a problem with your team structure."
"                        We found a circular reporting loop and no one in "
"that loop is linked to a user.                        Please double-check "
"that everyone reports to the correct manager."
msgstr ""
"Упс! Похоже, в структуре команды возникла проблема.                        "
"Обнаружена циклическая цепочка подчинения, и ни один сотрудник в этой "
"цепочке не связан с пользователем.                         Проверьте, чтобы "
"каждый сотрудник подчинялся корректному менеджеру."

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/core/web/thread_actions.js:0
msgid "Open Profile"
msgstr "Открытый профиль"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee_base.py:0
msgid "Operation not supported"
msgstr "Операция не поддерживается"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__other
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__gender__other
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__work_location_type__other
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__work_location_type__other
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__work_location_type__other
#: model:ir.model.fields.selection,name:hr.selection__hr_work_location__location_type__other
msgid "Other"
msgstr "Другое"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Our Product"
msgstr "Наш товар"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
msgid "Out of Working Hours"
msgstr "Вне рабочего времени"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_out_of_working_hour
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_presence_state__out_of_working_hour
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_out_of_working_hour
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_presence_state__out_of_working_hour
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_out_of_working_hour
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_presence_state__out_of_working_hour
msgid "Out of Working hours"
msgstr "Вне рабочего времени"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__pin
#: model:ir.model.fields,field_description:hr.field_res_users__pin
msgid "PIN"
msgstr "ПИН"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "PIN Code"
msgstr "ПИН-КОД"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__pin
#: model:ir.model.fields,help:hr.field_res_users__pin
msgid ""
"PIN used to Check In/Out in the Kiosk Mode of the Attendance application (if"
" enabled in Configuration) and to change the cashier in the Point of Sale "
"application."
msgstr ""
"PIN-код, используемый для регистрации входа/выхода в режиме киоска в модуле "
"Посещаемость (если он включен в конфигурации) и для смены кассира в модуле "
"POS."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__parent_id
msgid "Parent Department"
msgstr "Родительский отдел"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__parent_path
msgid "Parent Path"
msgstr "Родительский путь"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__user_partner_id
#: model:ir.model.fields,help:hr.field_hr_employee_public__user_partner_id
msgid "Partner-related data of the user"
msgstr "Данные о партнере пользователя"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Passion for software products"
msgstr "Страсть к программным продуктам"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__passport_id
#: model:ir.model.fields,field_description:hr.field_res_users__passport_id
msgid "Passport No"
msgstr "Паспорт"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Perfect written English"
msgstr "Безупречный письменный английский"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Perks"
msgstr "Компоненты"

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_permanent
msgid "Permanent"
msgstr "Постоянная"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Personal Evolution"
msgstr "Эволюция личности"

#. module: hr
#. odoo-python
#: code:addons/hr/models/res_users.py:0
msgid "Personal information update."
msgstr "Обновление личной информации."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__phone
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Phone"
msgstr "Телефон"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__place_of_birth
#: model:ir.model.fields,field_description:hr.field_res_users__place_of_birth
msgid "Place of Birth"
msgstr "Место рождения"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__plan_ids
msgid "Plan"
msgstr "План"

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan.py:0
msgid ""
"Plan %(plan_names)s cannot use a department as it is used only for some HR "
"plans."
msgstr ""
"План %(plan_names)s не может использовать отдел, так как он используется "
"только для некоторых HR‑планов."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_mail_activity_schedule__plan_department_filterable
msgid "Plan Department Filterable"
msgstr "Фильтрация по отделу для плана"

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan.py:0
msgid ""
"Plan activities %(template_names)s cannot use coach, manager or employee "
"responsible as it is used only for employee plans."
msgstr ""
"Действие %(template_names)s не может использовать куратора, менеджера или "
"сотрудника в качестве ответственного, так как оно предназначено только для "
"планов сотрудников."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_department_form
msgid "Plans"
msgstr "Планы"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__plans_count
msgid "Plans Count"
msgstr "Подсчет планов"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Play any sport with colleagues, <br>the bill is covered."
msgstr "Играйте в любой вид спорта с коллегами, <br>счет покрыт."

#. module: hr
#: model:ir.model.fields,help:hr.field_crm_team__alias_contact
#: model:ir.model.fields,help:hr.field_mail_alias__alias_contact
#: model:ir.model.fields,help:hr.field_mail_group__alias_contact
#: model:ir.model.fields,help:hr.field_maintenance_equipment_category__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Правило публикации сообщений в документе через почтовый шлюз:\n"
"- все: публиковать могут все\n"
"- партнеры: только аутентифицированные партнеры\n"
"- наблюдатели: только наблюдатели связанного документа или участники каналов\n"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Presence Condition"
msgstr "Условие присутствия"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Presence Display"
msgstr "Отображение присутствия"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Presence of employees"
msgstr "Присутствие сотрудников"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Presence reporting screen, email and IP address control."
msgstr ""
"Экран отчетов о присутствии, управление электронной почтой и IP-адресами."

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_presence_state__present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_presence_state__present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_presence_state__present
msgid "Present"
msgstr "Сейчас"

#. module: hr
#: model:ir.actions.report,name:hr.hr_employee_print_badge
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Print Badge"
msgstr "Печать бейджа"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Private Address"
msgstr "Домашний адрес"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_car_plate
msgid "Private Car Plate"
msgstr "Номерной знак частного автомобиля"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_city
#: model:ir.model.fields,field_description:hr.field_res_users__private_city
msgid "Private City"
msgstr "Частный город"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Private Contact"
msgstr "Частный контакт"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_country_id
#: model:ir.model.fields,field_description:hr.field_res_users__private_country_id
msgid "Private Country"
msgstr "Частная страна"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_email
#: model:ir.model.fields,field_description:hr.field_res_users__private_email
msgid "Private Email"
msgstr "Ваш Email"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Private Information"
msgstr "Личная информация"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_phone
#: model:ir.model.fields,field_description:hr.field_res_users__private_phone
msgid "Private Phone"
msgstr "Личный телефон"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_state_id
#: model:ir.model.fields,field_description:hr.field_res_users__private_state_id
msgid "Private State"
msgstr "Частное государство"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_street
#: model:ir.model.fields,field_description:hr.field_res_users__private_street
msgid "Private Street"
msgstr "Частная улица"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_street2
#: model:ir.model.fields,field_description:hr.field_res_users__private_street2
msgid "Private Street2"
msgstr "Частная улица2"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_zip
#: model:ir.model.fields,field_description:hr.field_res_users__private_zip
msgid "Private Zip"
msgstr "Частная молния"

#. module: hr
#: model:hr.department,name:hr.dep_ps
msgid "Professional Services"
msgstr "Профессиональные Услуги"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__employee_properties
msgid "Properties"
msgstr "Свойства"

#. module: hr
#: model:ir.model,name:hr.model_hr_employee_public
msgid "Public Employee"
msgstr "Государственный служащий"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Qualify the customer needs"
msgstr "Определите потребности клиента"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_employee_public_action
#: model_terms:ir.actions.act_window,help:hr.open_view_employee_list_my
msgid ""
"Quickly find all the information you need for your employees such as contact"
" data, job position, availability, etc."
msgstr ""
"Быстро находите всю необходимую информацию о сотрудниках: контактные данные,"
" должность, доступность и др."

#. module: hr
#: model:hr.department,name:hr.dep_rd_be
msgid "R&D USA"
msgstr "R&D США"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__rating_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__rating_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__rating_ids
msgid "Ratings"
msgstr "Рейтинги"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.action_hr_job
msgid "Ready to recruit more efficiently?"
msgstr "Готовы набирать персонал более эффективно?"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Real responsibilities and challenges in a fast evolving company"
msgstr "Реальные обязанности и задачи в быстро развивающейся компании"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__name
msgid "Reason"
msgstr "Причина"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__reason_code
msgid "Reason Code"
msgstr "Код причины"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_config_recruitment
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "Recruitment"
msgstr "Найм"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
#: model:ir.actions.act_window,name:hr.hr_departure_wizard_action
msgid "Register Departure"
msgstr "Регистрация Ухода"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form_smartbutton_inherited
msgid "Related Contacts"
msgstr "Связанные контакты"

#. module: hr
#. odoo-python
#: code:addons/hr/models/res_partner.py:0 code:addons/hr/models/res_users.py:0
msgid "Related Employees"
msgstr "Смежные сотрудники"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__related_partners_count
msgid "Related Partners Count"
msgstr "Количество связанных партнеров"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Related User"
msgstr "Связанный пользователь"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_ids
msgid "Related employee"
msgstr "Смежный сотрудник"

#. module: hr
#: model:ir.model.fields,help:hr.field_res_partner__employee_ids
msgid "Related employees based on their private address"
msgstr "Связанные сотрудники на основе их личного адреса"

#. module: hr
#: model:ir.model.fields,help:hr.field_resource_resource__user_id
msgid "Related user name for the resource to manage its access."
msgstr "Пользователь управляющий доступом к ресурсу."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__module_hr_homeworking
msgid "Remote Work"
msgstr "Удаленная работа"

#. module: hr
#: model:ir.ui.menu,name:hr.hr_menu_hr_reports
#: model:ir.ui.menu,name:hr.menu_hr_reporting_timesheet
msgid "Reporting"
msgstr "Отчет"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__requirements
msgid "Requirements"
msgstr "Требования"

#. module: hr
#: model:hr.department,name:hr.dep_rd
msgid "Research & Development"
msgstr "Отдел НИОКР"

#. module: hr
#: model:hr.departure.reason,name:hr.departure_resigned
msgid "Resigned"
msgstr "Совершил побег"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__resource_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__resource_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__resource_id
msgid "Resource"
msgstr "Ресурс"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__resource_calendar_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__resource_calendar_id
msgid "Resource Calendar"
msgstr "Календарь ресурсов"

#. module: hr
#: model:ir.model,name:hr.model_resource_calendar
msgid "Resource Working Time"
msgstr "Рабочее время"

#. module: hr
#: model:ir.model,name:hr.model_resource_resource
msgid "Resources"
msgstr "Ресурсы"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Responsibilities"
msgstr "Обязанности"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__activity_user_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_user_id
msgid "Responsible User"
msgstr "Ответственный пользователь"

#. module: hr
#: model:hr.departure.reason,name:hr.departure_retired
msgid "Retired"
msgstr "В отставке"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__sinid
msgid "SIN No"
msgstr "SIN No"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_has_sms_error
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_has_sms_error
#: model:ir.model.fields,field_description:hr.field_hr_job__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Ошибка доставки SMS"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__ssnid
#: model:ir.model.fields,field_description:hr.field_res_users__ssnid
msgid "SSN No"
msgstr "SSN Нет"

#. module: hr
#: model:hr.department,name:hr.dep_sales
msgid "Sales"
msgstr "Продажи"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_users_simple_form
msgid "Save"
msgstr "Сохранить"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Schedule"
msgstr "Запланировать встречу"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__study_school
#: model:ir.model.fields,field_description:hr.field_res_users__study_school
msgid "School"
msgstr "Школа"

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_seasonal
msgid "Seasonal"
msgstr "Сезон"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__coach_id
#: model:ir.model.fields,help:hr.field_hr_employee_base__coach_id
#: model:ir.model.fields,help:hr.field_hr_employee_public__coach_id
#: model:ir.model.fields,help:hr.field_res_users__coach_id
msgid ""
"Select the \"Employee\" who is the coach of this employee.\n"
"The \"Coach\" has no specific rights or responsibilities by default."
msgstr ""
"Выберите «Сотрудника», который является куратором этого сотрудника. \n"
"У «Куратора» по умолчанию нет специальных прав или обязанностей."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__sequence
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__sequence
#: model:ir.model.fields,field_description:hr.field_hr_job__sequence
msgid "Sequence"
msgstr "Последовательность"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Set default company schedule to manage your employees working time"
msgstr ""
"Установите расписание компании по умолчанию, чтобы управлять рабочим "
"временем ваших сотрудников"

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_config_settings_action
#: model:ir.ui.menu,name:hr.hr_menu_configuration
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Settings"
msgstr "Настройки"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__share
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__share
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__share
msgid "Share User"
msgstr "Поделиться Пользователь"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__show_hr_icon_display
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__show_hr_icon_display
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__show_hr_icon_display
#: model:ir.model.fields,field_description:hr.field_resource_resource__show_hr_icon_display
msgid "Show Hr Icon Display"
msgstr "Отображение значков часов"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/department_chart/department_chart.xml:0
msgid "Show employees"
msgstr "Показать сотрудников"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "Single"
msgstr "Один"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__module_hr_skills
msgid "Skills Management"
msgstr "Управление навыками"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__sinid
msgid "Social Insurance Number"
msgstr "Номер социального страхования"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__ssnid
#: model:ir.model.fields,help:hr.field_res_users__ssnid
msgid "Social Security Number"
msgstr "Номер социального страхования"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee_base.py:0
msgid "Some employee already have a work contact"
msgstr "У некоторых сотрудников уже есть рабочий контакт"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Sport Activity"
msgstr "Спортивная деятельность"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__spouse_birthdate
#: model:ir.model.fields,field_description:hr.field_res_users__spouse_birthdate
msgid "Spouse Birthdate"
msgstr "Дата рождения супруга"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__spouse_complete_name
#: model:ir.model.fields,field_description:hr.field_res_users__spouse_complete_name
msgid "Spouse Complete Name"
msgstr "Полное имя супруга"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Start Date"
msgstr "Дата начала"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "State"
msgstr "Область"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Status"
msgstr "Статус"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__activity_state
#: model:ir.model.fields,help:hr.field_hr_employee__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Статус в зависимости от дел\n"
"Просроченные: Срок выполнения уже прошел\n"
"Сегодня: Срок выполнения сегодня\n"
"Запланированные: Будущие дела."

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_statutaire
msgid "Statutory"
msgstr "Законодательно установленный"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Street 2..."
msgstr "Улица 2..."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Street..."
msgstr "Улица..."

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Strong analytical skills"
msgstr "Сильные аналитические навыки"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_department_kanban_action
#: model_terms:ir.actions.act_window,help:hr.hr_department_tree_action
msgid ""
"Structure Employees per department and have an overview of e.g.\n"
"                    expenses, timesheets, time off, recruitment, etc."
msgstr ""
"Структурируйте сотрудников по отделам и получайте обзор, например,\n"
"                    расходов, учета рабочего времени, отпусков, подбора персонала и т. д."

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_student
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__student
msgid "Student"
msgstr "Студент"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__name
msgid "Tag Name"
msgstr "Название тега"

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_employee_category_name_uniq
msgid "Tag name already exists!"
msgstr "Название тега уже существует!"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__category_ids
#: model:ir.ui.menu,name:hr.menu_view_employee_category_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Tags"
msgstr "Теги"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__no_of_recruitment
msgid "Target"
msgstr "Цель"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Technical Expertise"
msgstr "Техническая экспертиза"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__create_employee_id
msgid "Technical field, bind user to this employee on create"
msgstr ""
"Техническое поле, привязка пользователя к этому сотруднику при создании"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__create_employee
msgid "Technical field, whether to create an employee"
msgstr "Техническое поле, нужно ли создавать сотрудника"

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_temporary
msgid "Temporary"
msgstr "Временная"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid ""
"The Badge ID must be alphanumeric without any accents and no longer than 18 "
"characters."
msgstr ""
"Идентификатор бейджа должен быть буквенно‑цифровым, без диакритики и не "
"длиннее 18 символов."

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_employee_barcode_uniq
msgid ""
"The Badge ID must be unique, this one is already assigned to another "
"employee."
msgstr "ID бейджа должен быть уникальным, он уже присвоен другому сотруднику."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__company_country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"Код страны по ISO из двух символов.\n"
"Это поле можно использовать для быстрого поиска."

#. module: hr
#: model_terms:hr.job,description:hr.job_marketing
msgid ""
"The Marketing Manager defines the mid- to long-term marketing strategy for his covered market segments in the World.\n"
"              He develops and monitors the annual budget in collaboration with Sales.\n"
"              He defines the products and customers portfolio according to the marketing plan.\n"
"              This mission requires strong collaboration with Technical Service and Sales."
msgstr ""
"Менеджер по маркетингу определяет средне- и долгосрочную маркетинговую стратегию для своих сегментов рынка по всему миру.\n"
"              Он разрабатывает и контролирует годовой бюджет в сотрудничестве с отделом продаж.\n"
"              Он определяет портфель продуктов и клиентов в соответствии с маркетинговым планом.\n"
"              Эта миссия требует тесного сотрудничества с технической службой и отделом продаж."

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "The PIN must be a sequence of digits."
msgstr "ПИН должен быть последовательностью цифр."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "The default working hours are set in configuration."
msgstr "Стандартные часы работы задаются в настройках."

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan_template.py:0
msgid "The employee %s should be linked to a user."
msgstr "Сотрудник %s должен быть связан с пользователем."

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_job_no_of_recruitment_positive
msgid "The expected number of new employees must be positive."
msgstr "Ожидаемое количество новых сотрудников должно быть положительным."

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid ""
"The fields “%s”, which you are trying to read, are not available for "
"employee public profiles."
msgstr ""
"Поля «%s», которые вы пытаетесь просмотреть, недоступны в публичных профилях"
" сотрудников."

#. module: hr
#. odoo-python
#: code:addons/hr/models/res_users.py:0
msgid "The following fields were modified by %s"
msgstr "Следующие поля были изменены на %s"

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan_template.py:0
msgid "The manager of %s should be linked to a user."
msgstr "Менеджер %s должен быть связан с пользователем."

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_job_name_company_uniq
msgid "The name of the job position must be unique per department in company!"
msgstr ""
"Название должности должно быть уникальным для каждого отдела компании!"

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan_template.py:0
msgid "The user of %s's coach is not set."
msgstr "Пользователь тренера %s не установлен."

#. module: hr
#: model:res.groups,comment:hr.group_hr_user
msgid "The user will be able to approve document created by employees."
msgstr ""
"Данный пользователь сможет согласовывать документы, созданные сотрудниками."

#. module: hr
#: model:res.groups,comment:hr.group_hr_manager
msgid ""
"The user will have access to the human resources configuration as well as "
"statistic reports."
msgstr ""
"Пользователь будет иметь доступ к конфигурации отдела кадров, а также к "
"статистическим отчетам."

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "The work permit of %(employee)s expires at %(date)s."
msgstr "Срок действия разрешения на работу %(employee)s истекает в %(date)s."

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_thesis
msgid "Thesis"
msgstr "Дипломная работа"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "This employee already has an user."
msgstr "У этого сотрудника уже есть пользователь."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__tz
#: model:ir.model.fields,help:hr.field_hr_employee_base__tz
#: model:ir.model.fields,help:hr.field_hr_employee_public__tz
msgid ""
"This field is used in order to define in which timezone the employee will "
"work."
msgstr "Это поле задает часовой пояс, в котором работает сотрудник."

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/views/hr_graph_controller.xml:0
#: code:addons/hr/static/src/views/hr_pivot_controller.xml:0
msgid ""
"This report gives you an overview of your employees based on the measures of"
" your choice."
msgstr ""
"Этот отчет предоставляет обзор по сотрудникам на основе выбранных "
"показателей."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "This setting block is utilized to manage the frontend design."
msgstr ""
"Этот блок настроек используется для управления внешним видом интерфейса."

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan_template.py:0
msgid "Those responsible types are limited to Employee plans."
msgstr "Эти виды ответственности ограничиваются планами для сотрудников."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__tz
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__tz
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__tz
msgid "Timezone"
msgstr "Часовой пояс"

#. module: hr
#: model:digest.tip,name:hr.digest_tip_hr_0
#: model_terms:digest.tip,tip_description:hr.digest_tip_hr_0
msgid "Tip: Where's Bryan?"
msgstr "Совет: Где Брайан?"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid ""
"To avoid multi company issues (losing the access to your previous contracts,"
" leaves, ...), you should create another employee in the new company "
"instead."
msgstr ""
"Чтобы избежать проблем при работе с несколькими компаниями (потеря доступа к"
" предыдущим договорам, отпускам, ...), вам следует создать еще одного "
"сотрудника в новой компании."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Today Activities"
msgstr "Сегодняшние Дела"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__expected_employees
msgid "Total Forecasted Employees"
msgstr "Общее количество прогнозируемых сотрудников"

#. module: hr
#: model:hr.job,name:hr.job_trainee
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__trainee
msgid "Trainee"
msgstr "Стажер"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Trainings"
msgstr "Тренинги"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__activity_exception_decoration
#: model:ir.model.fields,help:hr.field_hr_employee__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Тип Дела для исключения в записи."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "Unarchive"
msgstr "Разархивировать"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_undetermined
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_undetermined
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_undetermined
msgid "Undetermined"
msgstr "Не определено"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Unread Messages"
msgstr "Непрочитанные сообщения"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.open_view_categ_form
msgid "Use tags to categorize your Employees."
msgstr "Используйте теги, чтобы распределить сотрудников по категориям."

#. module: hr
#: model:ir.model,name:hr.model_res_users
#: model:ir.model.fields,field_description:hr.field_hr_employee__user_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__user_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__user_id
#: model:ir.model.fields,field_description:hr.field_resource_resource__user_id
msgid "User"
msgstr "Пользователь"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__user_partner_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__user_partner_id
msgid "User's partner"
msgstr "Партнер пользователя"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_job_view_kanban
msgid "Vacancies:"
msgstr "Вакансии:"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_company__hr_presence_control_ip_list
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_ip_list
msgid "Valid IP addresses"
msgstr "Действительные IP-адреса"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Valid work permit for Belgium"
msgstr "Действительное разрешение на работу в Бельгии"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__visa_expire
#: model:ir.model.fields,field_description:hr.field_res_users__visa_expire
msgid "Visa Expiration Date"
msgstr "Дата окончания срока действия визы"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__visa_no
#: model:ir.model.fields,field_description:hr.field_res_users__visa_no
msgid "Visa No"
msgstr "Номер банковской карты"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "Warning"
msgstr "Предупреждение"

#. module: hr
#: model_terms:hr.job,description:hr.job_consultant
msgid ""
"We are currently looking for someone like that to join our Consultant team."
msgstr ""
"В настоящее время мы ищем такого человека в нашу команду консультантов."

#. module: hr
#: model_terms:hr.job,description:hr.job_developer
msgid ""
"We are currently looking for someone like that to join our Web team.\n"
"                Someone who can snap out of coding and perform analysis or meet clients to explain the technical possibilities that can meet their needs."
msgstr ""
"В настоящее время мы ищем такого человека в нашу веб-команду.\n"
"                Того, кто может оторваться от программирования и провести анализ или встретиться с клиентами, чтобы объяснить им технические возможности, которые могут удовлетворить их потребности."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__website_message_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__website_message_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__website_message_ids
msgid "Website Messages"
msgstr "Веб-сайт сообщения"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__website_message_ids
#: model:ir.model.fields,help:hr.field_hr_employee__website_message_ids
#: model:ir.model.fields,help:hr.field_hr_job__website_message_ids
msgid "Website communication history"
msgstr "История общений с сайта"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "What We Offer"
msgstr "Наше предложение"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "What's great in the job?"
msgstr "Что хорошего в этой работе?"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__member_of_department
#: model:ir.model.fields,help:hr.field_hr_employee_base__member_of_department
#: model:ir.model.fields,help:hr.field_hr_employee_public__member_of_department
msgid ""
"Whether the employee is a member of the active user's department or one of "
"it's child department."
msgstr ""
"Является ли сотрудник сотрудником отдела активного пользователя или одного "
"из его дочерних отделов."

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "Widower"
msgstr "Вдовец"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__address_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__address_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__address_id
#: model:ir.model.fields,field_description:hr.field_hr_work_location__address_id
#: model:ir.model.fields,field_description:hr.field_res_users__address_id
msgid "Work Address"
msgstr "Рабочий адрес"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_contact_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_contact_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_contact_id
#: model:ir.model.fields,field_description:hr.field_res_users__work_contact_id
msgid "Work Contact"
msgstr "Рабочий контакт"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_email
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_email
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_email
#: model:ir.model.fields,field_description:hr.field_res_users__work_email
#: model:ir.model.fields,field_description:hr.field_resource_resource__work_email
msgid "Work Email"
msgstr "Рабочая электронная почта"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Work Information"
msgstr "Информация о работе"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/avatar_card/avatar_card_popover_patch.xml:0
#: model:ir.model,name:hr.model_hr_work_location
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_location_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_location_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_location_id
#: model:ir.model.fields,field_description:hr.field_hr_work_location__name
#: model:ir.model.fields,field_description:hr.field_res_users__work_location_id
#: model_terms:ir.ui.view,arch_db:hr.hr_work_location_form_view
#: model_terms:ir.ui.view,arch_db:hr.hr_work_location_tree_view
msgid "Work Location"
msgstr "Место работы"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_location_name
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_location_name
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_location_name
#: model:ir.model.fields,field_description:hr.field_res_users__work_location_name
msgid "Work Location Name"
msgstr "Название места работы"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_location_type
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_location_type
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_location_type
#: model:ir.model.fields,field_description:hr.field_res_users__work_location_type
msgid "Work Location Type"
msgstr "Тип места работы"

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_work_location_action
#: model:ir.ui.menu,name:hr.menu_hr_work_location_tree
msgid "Work Locations"
msgstr "Рабочие места"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__mobile_phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__mobile_phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__mobile_phone
#: model:ir.model.fields,field_description:hr.field_res_users__mobile_phone
msgid "Work Mobile"
msgstr "Рабочий мобильный телефон"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Work Organization"
msgstr "Организация работы"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__has_work_permit
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Work Permit"
msgstr "Разрешение на работу"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_permit_expiration_date
msgid "Work Permit Expiration Date"
msgstr "Дата истечения срока действия разрешения на работу"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__permit_no
#: model:ir.model.fields,field_description:hr.field_res_users__permit_no
msgid "Work Permit No"
msgstr "Разрешение на работу"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_permit_scheduled_activity
msgid "Work Permit Scheduled Activity"
msgstr "Разрешение на работу Запланированная деятельность"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_phone
#: model:ir.model.fields,field_description:hr.field_res_users__work_phone
#: model:ir.model.fields,field_description:hr.field_resource_resource__work_phone
msgid "Work Phone"
msgstr "Рабочий телефон"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__worker
msgid "Worker"
msgstr "Исполнитель"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__resource_calendar_id
msgid "Working Hours"
msgstr "Часы работы"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_resource_calendar_view
msgid "Working Schedules"
msgstr "Рабочие графики"

#. module: hr
#. odoo-python
#: code:addons/hr/models/res_users.py:0
msgid ""
"You are not allowed to create an employee because the user does not have "
"access rights for %s"
msgstr ""
"У вас нет прав создать сотрудника, так как у пользователя нет доступа к  %s"

#. module: hr
#. odoo-python
#: code:addons/hr/models/res_users.py:0
msgid ""
"You are only allowed to update your preferences. Please contact a HR officer"
" to update other information."
msgstr ""
"Вы можете обновить только свои предпочтения. Чтобы обновить другую "
"информацию, обратитесь к сотруднику отдела кадров."

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/store_service_patch.js:0
msgid "You can only chat with employees that have a dedicated user."
msgstr ""
"Вы можете общаться только с сотрудниками, для которых есть пользователь в "
"системе."

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_department.py:0
msgid "You cannot create recursive departments."
msgstr "Нельзя создавать рекурсивные подразделения."

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "You do not have access to this document."
msgstr "У вас нет доступа к этому документу."

#. module: hr
#: model_terms:hr.job,description:hr.job_trainee
msgid ""
"You participate to the update of our tutorial tools and pre-sales tools after the launch of a new version of Odoo. Indeed, any new version of the software brings significant improvements in terms of functionalities, ergonomics and configuration.\n"
"You will have to become familiar with the existing tools (books, class supports, Odoo presentation’s slides, commercial tools),\n"
"to participate to the update of those tools in order to make them appropriate for the new version of the software and, for sure,\n"
"to suggest improvements in order to cover the new domains of the software.\n"
"You join the Implementation Assistance department. This team of 3 people go with Odoo’s clients in the set up of the software. Your role will be\n"
"to animate webinars in order to show the different functionalities of the software.\n"
"to be involved in the support of the customers and\n"
"to answer to their questions.\n"
"You help the support manager to set up new support services by\n"
"being involved in the treatment of new cases,\n"
"contributing to the set up of a new politic,\n"
"being involved into satisfaction surveys in order to have a better knowledge of how the support given is seen by the customers."
msgstr ""
"Вы будете принимать участие в обновлении наших обучающих инструментов и инструментов предпродажной подготовки после запуска новой версии Odoo. Ведь любая новая версия программного обеспечения приносит значительные улучшения в плане функциональности, эргономики и конфигурации.\n"
"Вам нужно будет ознакомиться с существующими инструментами (книги, учебные пособия, слайды презентаций Odoo, коммерческие инструменты),\n"
"принять участие в обновлении этих инструментов, чтобы привести их в соответствие с новой версией программного обеспечения, и, конечно же,\n"
"предложить улучшения, чтобы охватить новые возможности программного обеспечения.\n"
"Вы присоединитесь к отделу помощи по внедрению. Эта команда из 3 человек помогает клиентам Odoo в установке программного обеспечения. Ваша роль будет заключаться в следующем:\n"
"анимация вебинаров для демонстрации различных функциональных возможностей программного обеспечения;\n"
"участие в поддержке клиентов;\n"
"предоставление ответов на их вопросы.\n"
"Вы будете помогать менеджеру службы поддержки создавать новые службы поддержки:\n"
"участвуя в рассмотрении новых случаев;\n"
"внося свой вклад в создание новой политики;\n"
"участвуя в опросах удовлетворенности, чтобы лучше понять, как клиенты воспринимают оказываемую поддержку."

#. module: hr
#: model_terms:hr.job,description:hr.job_cto
msgid ""
"You will take part in the consulting services we provide to our partners and customers: design, analysis, development, testing, project management, support/coaching. You will work autonomously as well as coordinate and supervise small distributed development teams for some projects. Optionally, you will deliver Odoo training sessions to partners and customers (8-10 people/session). You will report to the Head of Professional Services and work closely with all developers and consultants.\n"
"\n"
"The job is located in Grand-Rosière (1367), Belgium (between Louvain-La-Neuve and Namur)."
msgstr ""
"You will take part in the consulting services we provide to our partners and customers: design, analysis, development, testing, project management, support/coaching. You will work autonomously as well as coordinate and supervise small distributed development teams for some projects. Optionally, you will deliver Odoo training sessions to partners and customers (8-10 people/session). You will report to the Head of Professional Services and work closely with all developers and consultants.\n"
"\n"
"The job is located in Grand-Rosière (1367), Belgium (between Louvain-La-Neuve and Namur)."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "ZIP"
msgstr "ИНДЕКС"

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_alias.py:0
msgid "addresses linked to registered employees"
msgstr "адреса, связанные с зарегистрированными сотрудниками"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_department_form
msgid "department"
msgstr "подразделение"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "e.g. Building 2, Remote, etc."
msgstr "например,  Корпус  2,  Удаленно и т. д."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
msgid "e.g. John Doe"
msgstr "например, Васильев Андрей"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "e.g. Sales Manager"
msgstr "нап. Менеджер по продажам"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "e.g. Summarize the position in one or two lines..."
msgstr "например, опишите должность в одной‑двух строках…"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "e.g. <EMAIL>"
msgstr "например, <EMAIL>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "e.g. <EMAIL>"
msgstr "например, <EMAIL>"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__distance_home_work_unit__kilometers
msgid "km"
msgstr "км"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__distance_home_work_unit__miles
msgid "mi"
msgstr "мили"

#. module: hr
#. odoo-python
#: code:addons/hr/models/models.py:0
msgid "restricted to employees"
msgstr "только для сотрудников"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_permit_name
msgid "work_permit_name"
msgstr "work_permit_name"
