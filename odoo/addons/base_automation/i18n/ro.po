# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_automation
# 
# Translators:
# <PERSON><PERSON>, 2024
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>_nexterp, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 12:50+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Larisa_nexterp, 2025\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
msgid ""
"\"\n"
"                (ID:"
msgstr ""
"\"\n"
"                (ID:"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/kanban_header_patch.js:0
msgid "\"%s\" tag is added"
msgstr "Eticheta \"%s” este adăugată"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
msgid ""
"\"On live update\" automation rules can only be used with \"Execute Python "
"Code\" action type."
msgstr ""
"Regulile de automatizare „La actualizare live” pot fi utilizate doar cu "
"tipul de acțiune „Execută cod Python”."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "%s actions"
msgstr "%s acțiuni"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "1 action"
msgstr "1 acțiune"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<code>env</code>: environment on which the action is triggered"
msgstr "<code>env</code>: mediul pe care este declanșată acțiunea"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<code>model</code>: model of the record on which the action is triggered; is"
" a void recordset"
msgstr ""
"<code>model</code>: modelul înregistrării pe care este declanșată acțiunea; "
"este un set de înregistrări gol"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<code>payload</code>: the payload of the call (GET parameters, JSON body), "
"as a dict."
msgstr ""
"<code>payload</code>: sarcina utilă a apelului (parametri GET, corp JSON), "
"ca dicționar."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<code>time</code>, <code>datetime</code>, <code>dateutil</code>, "
"<code>timezone</code>: useful Python libraries"
msgstr ""
"<code>time</code>, <code>datetime</code>, <code>dateutil</code>, "
"<code>timezone</code>: biblioteci Python utile"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_kanban
msgid "<i class=\"fa fa-2x fa-arrow-right text-primary\" title=\"Actions\"/>"
msgstr "<i class=\"fa fa-2x fa-arrow-right text-primary\" title=\"Acțiuni\"/>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<i class=\"fa fa-info-circle\"/> The default target record getter will work "
"out-of-the-box for any webhook coming from another Odoo instance."
msgstr ""
"<i class=\"fa fa-info-circle\"/> Selectorul implicit de înregistrare țintă "
"va funcționa direct pentru orice webhook provenit dintr-o altă instanță "
"Odoo."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<i class=\"fa fa-warning\"/> Automation rules triggered by UI changes will "
"be executed <em>every time</em> the watched fields change, <em>whether you "
"save or not</em>."
msgstr ""
"<i class=\"fa fa-warning\"/> Regulile de automatizare declanșate de "
"modificările UI vor fi executate <em>de fiecare dată</em> când câmpurile "
"urmărite se modifică, <em>indiferent dacă salvați sau nu</em>."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<span class=\"text-muted\"> Available variables: </span>"
msgstr "<span class=\"text-muted\"> Variabile disponibile: </span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<span invisible=\"evaluation_type != 'value'\">to</span>\n"
"                                                        <span invisible=\"evaluation_type != 'equation'\">as</span>"
msgstr ""
"<span invisible=\"evaluation_type != 'value'\">la</span>\n"
"                                                        <span invisible=\"evaluation_type != 'equation'\">ca</span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<span invisible=\"trigger != 'on_time_created'\">after creation</span>\n"
"                                    <span invisible=\"trigger != 'on_time_updated'\">after last update</span>\n"
"                                    <span invisible=\"trigger != 'on_time'\">after</span>"
msgstr ""
"<span invisible=\"trigger != 'on_time_created'\">după creare</span>\n"
"                                    <span invisible=\"trigger != 'on_time_updated'\">după ultima actualizare</span>\n"
"                                    <span invisible=\"trigger != 'on_time'\">după</span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<span>by adding</span>"
msgstr "<span>prin adăugare</span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<span>by clearing it</span>"
msgstr "<span>prin golire</span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<span>by removing</span>"
msgstr "<span>prin eliminare</span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<span>by setting it to</span>"
msgstr "<span>prin setare la</span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<strong><i class=\"fa fa-lock\"/> Keep it secret, keep it safe.</strong>"
msgstr ""
"<strong><i class=\"fa fa-lock\"/> Păstrați-l secret, păstrați-l în "
"siguranță.</strong>"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_ir_actions_server__name
#: model:ir.model.fields,field_description:base_automation.field_ir_cron__name
msgid "Action Name"
msgstr "Nume acțiune"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__action_server_ids
msgid "Actions"
msgstr "Acțiuni"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Actions To Do"
msgstr "Acțiuni de efectuat"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__active
msgid "Active"
msgstr "Activ"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Add an action"
msgstr "Adaugă o acțiune"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "Add followers"
msgstr "Adaugă urmăritori"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
msgid "Add followers: %(partner_names)s"
msgstr "Adaugă urmăritori: %(partner_names)s"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_time_created
msgid "After creation"
msgstr "După creare"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_time_updated
msgid "After last update"
msgstr "După ultima actualizare"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__filter_domain
msgid "Apply on"
msgstr "Aplică pe"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_kanban
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_search
msgid "Archived"
msgstr "Arhivat"

#. module: base_automation
#: model_terms:ir.actions.act_window,help:base_automation.base_automation_act
msgid "Automate <em>everything</em> with Automation Rules"
msgstr "Automatizează <em>totul</em> cu Regulile de automatizare"

#. module: base_automation
#: model:ir.model,name:base_automation.model_base_automation
#: model:ir.model.fields,field_description:base_automation.field_ir_actions_server__base_automation_id
#: model:ir.model.fields,field_description:base_automation.field_ir_cron__base_automation_id
#: model:ir.model.fields.selection,name:base_automation.selection__ir_actions_server__usage__base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Automation Rule"
msgstr "Regulă de automatizare"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__name
msgid "Automation Rule Name"
msgstr "Nume regulă de automatizare"

#. module: base_automation
#: model:ir.actions.act_window,name:base_automation.base_automation_act
#: model:ir.ui.menu,name:base_automation.menu_base_automation_form
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_kanban
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_tree
msgid "Automation Rules"
msgstr "Reguli de automatizare"

#. module: base_automation
#: model:ir.actions.server,name:base_automation.ir_cron_data_base_automation_check_ir_actions_server
msgid "Automation Rules: check and execute"
msgstr "Reguli de automatizare: verifică și execută"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/kanban_header_patch.js:0
msgid "Automations"
msgstr "Automatizări"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_time
msgid "Based on date field"
msgstr "Pe baza câmpului de dată"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__filter_pre_domain
msgid "Before Update Domain"
msgstr "Domeniu înainte de actualizare"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"Change the URL's secret if you think the URL is no longer secure. You will "
"have to update any automated system that calls this webhook to the new URL."
msgstr ""
"Schimbați secretul URL-ului dacă considerați că acesta nu mai este sigur. Va"
" trebui să actualizați orice sistem automatizat care apelează acest webhook "
"la noul URL."

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
msgid "Compute"
msgstr "Calculează"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
msgid "Create %(model_name)s with name %(value)s"
msgstr "Creează %(model_name)s cu numele %(value)s"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "Create a new Record"
msgstr "Creează o înregistrare nouă"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
msgid "Create activity: %(activity_name)s"
msgstr "Creează activitate: %(activity_name)s"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "Create next activity"
msgstr "Creează următoarea activitate"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__create_date
msgid "Created on"
msgstr "Creat la"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_trigger_selection_field.js:0
msgid "Custom"
msgstr "Personalizat"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Date Field"
msgstr "Câmp dată"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__day
msgid "Days"
msgstr "Zile"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Delay"
msgstr "Întârziere"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_range
msgid ""
"Delay after the trigger date. You can put a negative number if you need a "
"delay before the trigger date, like sending a reminder 15 minutes before a "
"meeting."
msgstr ""
"Întârziere după data declanșării. Puteți introduce un număr negativ dacă "
"aveți nevoie de o întârziere înainte de data declanșării, de exemplu pentru "
"a trimite un memento cu 15 minute înainte de o întâlnire."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_range
msgid "Delay after trigger date"
msgstr "Întârziere după data declanșării"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_range_type
msgid "Delay type"
msgstr "Tip întârziere"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Delete Action"
msgstr "Șterge acțiunea"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_trigger_selection_field.js:0
msgid "Deprecated (do not use)"
msgstr "Depreciat (nu folosiți)"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__description
msgid "Description"
msgstr "Descriere"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
msgid "Disable Automation Rule"
msgstr "Dezactivează regula de automatizare"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
msgid ""
"Disabling this automation rule will enable you to continue your workflow\n"
"                but any data created after this could potentially be corrupted,\n"
"                as you are effectively disabling a customization that may set\n"
"                important and/or required fields."
msgstr ""
"Dezactivarea acestei reguli de automatizare vă va permite să continuați fluxul de lucru,\n"
"                dar orice date create ulterior pot fi corupte,\n"
"                deoarece dezactivați o personalizare care poate seta\n"
"                câmpuri importante și/sau obligatorii."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
msgid "Edit Automation Rule"
msgstr "Editează regula de automatizare"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "Email"
msgstr "E-mail"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_trigger_selection_field.js:0
msgid "Email Events"
msgstr "Evenimente e-mail"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
msgid ""
"Email, follower or activity action types cannot be used when deleting "
"records, as there are no more records to apply these changes to!"
msgstr ""
"Tipurile de acțiuni e-mail, urmăritor sau activitate nu pot fi folosite la "
"ștergerea înregistrărilor, deoarece nu mai există înregistrări pentru a "
"aplica aceste modificări!"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "Execute Python Code"
msgstr "Execută cod Python"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "Execute several actions"
msgstr "Execută mai multe acțiuni"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_trigger_selection_field.js:0
msgid "External"
msgstr "Extern"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Extra Conditions"
msgstr "Condiții suplimentare"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__on_change_field_ids
msgid "Fields that trigger the onchange."
msgstr "Câmpuri care declanșează modificarea."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "Generic User"
msgstr "Utilizator generic"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__model_is_mail_thread
msgid "Has Mail Thread"
msgstr "Are fir de e-mail"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__hour
msgid "Hours"
msgstr "Ore"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__id
msgid "ID"
msgstr "ID"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__filter_domain
msgid ""
"If present, this condition must be satisfied before executing the automation"
" rule."
msgstr ""
"Dacă este prezentă, această condiție trebuie să fie îndeplinită înainte de "
"executarea regulii de automatizare."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__filter_pre_domain
msgid ""
"If present, this condition must be satisfied before the update of the "
"record. Not checked on record creation."
msgstr ""
"Dacă este prezentă, această condiție trebuie să fie îndeplinită înainte de "
"actualizarea înregistrării. Nu se verifică la crearea înregistrării."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Keep track of what this automation does and why it exists..."
msgstr ""
"Țineți evidența a ceea ce face această automatizare și de ce există..."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__last_run
msgid "Last Run"
msgstr "Ultima rulare"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare la"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__least_delay_msg
msgid "Least Delay Msg"
msgstr "Mesaj întârziere minimă"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__log_webhook_calls
msgid "Log Calls"
msgstr "Înregistrează apelurile"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Logs"
msgstr "Jurnale"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
msgid ""
"Mail event can not be configured on model %s. Only models with discussion "
"feature can be used."
msgstr ""
"Evenimentul de e-mail nu poate fi configurat pe modelul %s. Doar modelele cu"
" funcționalitate de discuții pot fi utilizate."

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__minutes
msgid "Minutes"
msgstr "Minute"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__model_id
msgid "Model"
msgstr "Model"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__model_name
msgid "Model Name"
msgstr "Nume model"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
msgid ""
"Model of action %(action_name)s should match the one from automated rule "
"%(rule_name)s."
msgstr ""
"Modelul acțiunii %(action_name)s trebuie să fie același cu cel din regula "
"automată %(rule_name)s."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__model_id
msgid "Model on which the automation rule runs."
msgstr "Modelul pe care rulează regula de automatizare."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__month
msgid "Months"
msgstr "Luni"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
msgid "No record to run the automation on was found."
msgstr "Nu a fost găsită nicio înregistrare pentru a rula automatizarea."

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
msgid ""
"Note that this automation rule can be triggered up to %d minutes after its "
"schedule."
msgstr ""
"Rețineți că această regulă de automatizare poate fi declanșată până la %d "
"minute după programarea sa."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Notes"
msgstr "Note"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__on_change_field_ids
msgid "On Change Fields Trigger"
msgstr "Declanșator la modificarea câmpurilor"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_change
msgid "On UI change"
msgstr "La modificarea interfeței"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_archive
msgid "On archived"
msgstr "La arhivare"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_create
msgid "On creation"
msgstr "La creare"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_unlink
msgid "On deletion"
msgstr "La ștergere"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_message_received
msgid "On incoming message"
msgstr "La primirea mesajului"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_message_sent
msgid "On outgoing message"
msgstr "La trimiterea mesajului"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_create_or_write
msgid "On save"
msgstr "La salvare"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_unarchive
msgid "On unarchived"
msgstr "La dezarhivare"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_write
msgid "On update"
msgstr "La actualizare"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_webhook
msgid "On webhook"
msgstr "Pe webhook"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "Post as Message"
msgstr "Postează ca mesaj"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "Post as Note"
msgstr "Postează ca notă"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_priority_set
msgid "Priority is set to"
msgstr "Prioritatea este setată la"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__record_getter
msgid "Record Getter"
msgstr "Obținere înregistrare"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "Remove followers"
msgstr "Elimină urmăritorii"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
msgid "Remove followers: %(partner_names)s"
msgstr "Elimină urmăritorii: %(partner_names)s"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Rotate Secret"
msgstr "Schimbă secretul"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Select a date field..."
msgstr "Selectează un câmp de dată..."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Select a value..."
msgstr "Selectează o valoare..."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Select fields..."
msgstr "Selectează câmpuri..."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "Send SMS"
msgstr "Trimite SMS"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
msgid "Send SMS: %(template_name)s"
msgstr "Trimite SMS: %(template_name)s"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
msgid "Send Webhook Notification"
msgstr "Trimite notificare Webhook"

#. module: base_automation
#: model_terms:ir.actions.act_window,help:base_automation.base_automation_act
msgid ""
"Send an email when an object changes state, archive records\n"
"                after a month of inactivity or remind yourself to follow-up on\n"
"                tasks when a specific tag is added."
msgstr ""
"Trimite un e-mail când un obiect își schimbă starea, arhivează înregistrările\n"
"                după o lună de inactivitate sau reamintește-ți să urmărești\n"
"                sarcinile când este adăugată o etichetă specifică."

#. module: base_automation
#: model_terms:digest.tip,tip_description:base_automation.digest_tip_base_automation_0
msgid ""
"Send an email when an object changes state, archive records after a month of"
" inactivity or remind yourself to follow-up on tasks when a specific tag is "
"added.<br>With Automation Rules, you can automate any workflow."
msgstr ""
"Trimite un e-mail când un obiect își schimbă starea, arhivează "
"înregistrările după o lună de inactivitate sau reamintește-ți să urmărești "
"sarcinile când este adăugată o etichetă specifică.<br>Cu Regulile de "
"automatizare, poți automatiza orice flux de lucru."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "Send email"
msgstr "Trimite e-mail"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
msgid "Send email: %(template_name)s"
msgstr "Trimite e-mail: %(template_name)s"

#. module: base_automation
#: model:ir.model,name:base_automation.model_ir_actions_server
msgid "Server Action"
msgstr "Acțiune server"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_selection_field_id
msgid ""
"Some triggers need a reference to a selection field. This field is used to "
"store it."
msgstr ""
"Unele declanșatoare au nevoie de o referință la un câmp de selecție. Acest "
"câmp este folosit pentru a o stoca."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_field_ref
msgid ""
"Some triggers need a reference to another field. This field is used to store"
" it."
msgstr ""
"Unele declanșatoare au nevoie de o referință la un alt câmp. Acest câmp este"
" folosit pentru a o stoca."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "Specific User"
msgstr "Utilizator specific"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_stage_set
msgid "Stage is set to"
msgstr "Etapa este setată la"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/kanban_header_patch.js:0
msgid "Stage is set to \"%s\""
msgstr "Etapa este setată la \"%s\""

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_state_set
msgid "State is set to"
msgstr "Starea este setată la"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_tag_set
msgid "Tag is added"
msgstr "Eticheta este adăugată"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Target Record"
msgstr "Înregistrare țintă"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
msgid ""
"Target model of actions %(action_names)s are different from rule model."
msgstr ""
"Modelul țintă al acțiunilor %(action_names)s este diferit de modelul "
"regulii."

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
msgid ""
"The \"%(trigger_value)s\" %(trigger_label)s can only be used with the "
"\"%(state_value)s\" action type"
msgstr ""
"„%(trigger_value)s” %(trigger_label)s poate fi folosit doar cu tipul de "
"acțiune „%(state_value)s”"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trigger_field_ids
msgid ""
"The automation rule will be triggered if and only if one of these fields is "
"updated.If empty, all fields are watched."
msgstr ""
"Regula de automatizare va fi declanșată dacă și numai dacă unul dintre "
"aceste câmpuri este actualizat. Dacă este gol, toate câmpurile sunt "
"urmărite."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
msgid ""
"The error occurred during the execution of the automation rule\n"
"                \""
msgstr ""
"Eroarea a apărut în timpul executării regulii de automatizare\n"
"                \""

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__record_getter
msgid ""
"This code will be run to find on which record the automation rule should be "
"run."
msgstr ""
"Acest cod va fi rulat pentru a determina pe ce înregistrare trebuie să fie "
"executată regula de automatizare."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_trigger_selection_field.js:0
msgid "Timing Conditions"
msgstr "Condiții de timp"

#. module: base_automation
#: model:digest.tip,name:base_automation.digest_tip_base_automation_0
#: model_terms:digest.tip,tip_description:base_automation.digest_tip_base_automation_0
msgid "Tip: Automate everything with Automation Rules"
msgstr "Sfat: Automatizează totul cu Regulile de automatizare"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trigger
msgid "Trigger"
msgstr "Declanșator"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_id
msgid "Trigger Date"
msgstr "Data declanșării"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_selection_field_id
msgid "Trigger Field"
msgstr "Câmp declanșator"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_field_ref_model_name
msgid "Trigger Field Model"
msgstr "Model câmp declanșator"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trigger_field_ids
msgid "Trigger Fields"
msgstr "Câmpuri declanșatoare"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_field_ref
msgid "Trigger Reference"
msgstr "Referință declanșator"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.ir_actions_server_view_form_automation
msgid "Type"
msgstr "Tip"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "URL"
msgstr "URL"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "URL will be created once the rule is saved."
msgstr "URL-ul va fi creat după ce regula este salvată."

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
msgid "Update"
msgstr "Actualizează"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "Update the Record"
msgstr "Actualizează înregistrarea"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__url
msgid "Url"
msgstr "Url"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_ir_actions_server__usage
#: model:ir.model.fields,field_description:base_automation.field_ir_cron__usage
msgid "Usage"
msgstr "Utilizare"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_calendar_id
msgid "Use Calendar"
msgstr "Folosește calendar"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_user_set
msgid "User is set"
msgstr "Utilizatorul este setat"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_trigger_selection_field.js:0
msgid "Values Updated"
msgstr "Valori actualizate"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
msgid "Warning"
msgstr "Avertizare"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
msgid "Webhook Log"
msgstr "Jurnal Webhook"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
msgid "Webhook Logs"
msgstr "Jurnale Webhook"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__webhook_uuid
msgid "Webhook UUID"
msgstr "Webhook UUID"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "Weeks"
msgstr "Săptămâni"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_calendar_id
msgid ""
"When calculating a day-based timed condition, it is possibleto use a "
"calendar to compute the date based on working days."
msgstr ""
"Când se calculează o condiție de timp bazată pe zile, se poate folosi un "
"calendar pentru a calcula data pe baza zilelor lucrătoare."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_id
msgid ""
"When should the condition be triggered.\n"
"                If present, will be checked by the scheduler. If empty, will be checked at creation and update."
msgstr ""
"Când ar trebui să fie declanșată condiția.\n"
"                Dacă este prezentă, va fi verificată de programator. Dacă este goală, va fi verificată la creare și actualizare."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__active
msgid "When unchecked, the rule is hidden and will not be executed."
msgstr "Când nu este bifată, regula este ascunsă și nu va fi executată."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "When updating"
msgstr "La actualizare"

#. module: base_automation
#: model_terms:ir.actions.act_window,help:base_automation.base_automation_act
msgid ""
"With Automation Rules, you can automate\n"
"                <em>any</em> workflow."
msgstr ""
"Cu Regulile de automatizare, poți automatiza\n"
"                <em>orice</em> flux de lucru."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
msgid ""
"You can ask an administrator to disable or correct this automation rule."
msgstr ""
"Poți cere unui administrator să dezactiveze sau să corecteze această regulă "
"de automatizare."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
msgid "You can disable this automation rule or edit it to solve the issue."
msgstr ""
"Poți dezactiva această regulă de automatizare sau o poți edita pentru a "
"rezolva problema."

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
msgid ""
"You cannot send an email, add followers or create an activity for a deleted "
"record.  It simply does not work."
msgstr ""
"Nu poți trimite un e-mail, adăuga urmăritori sau crea o activitate pentru o "
"înregistrare ștearsă. Pur și simplu nu funcționează."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"Your webhook URL contains a secret. Don't share it online or carelessly."
msgstr ""
"URL-ul webhook-ului tău conține un secret. Nu îl distribui online sau "
"neglijent."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_kanban
msgid "based on"
msgstr "bazat pe"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "e.g. Support flow"
msgstr "ex: flux suport"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.xml:0
msgid "no action defined..."
msgstr "nicio acțiune definită..."
