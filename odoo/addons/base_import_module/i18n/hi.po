# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_import_module
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2025
# <PERSON><PERSON>, 2025
# <PERSON><PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-19 15:46+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>j<PERSON><PERSON>ak, 2025\n"
"Language-Team: Hindi (https://app.transifex.com/odoo/teams/41243/hi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid ""
"\n"
"You may need the Enterprise version to install the data module. Please visit https://www.odoo.com/pricing-plan for more information.\n"
"If you need Website themes, it can be downloaded from https://github.com/odoo/design-themes.\n"
msgstr ""

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.module_form_apps_inherit
#: model_terms:ir.ui.view,arch_db:base_import_module.module_view_kanban_apps_inherit
msgid "Activate"
msgstr ""

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#: model_terms:ir.ui.view,arch_db:base_import_module.view_module_filter_apps_inherit
msgid "Apps"
msgstr ""

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Cancel"
msgstr "रद्द"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Close"
msgstr "बंद"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid ""
"Connection to %(url)s failed, the module %(module)s cannot be downloaded."
msgstr ""

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid "Connection to %s failed The list of industry modules cannot be fetched"
msgstr ""

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/controllers/main.py:0
msgid "Could not select database '%s'"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__create_uid
msgid "Created by"
msgstr "द्वारा निर्मित"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__create_date
msgid "Created on"
msgstr "इस तारीख को बनाया गया"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__display_name
msgid "Display Name"
msgstr "डिस्प्ले नाम"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid ""
"Error while importing module '%(module)s'.\n"
"\n"
" %(error_message)s \n"
"\n"
msgstr ""

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid "File '%s' exceed maximum allowed file size"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__force
msgid "Force init"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,help:base_import_module.field_base_import_module__force
msgid ""
"Force init mode even if installed. (will update `noupdate='1'` records)"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__id
msgid "ID"
msgstr "आईडी"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__import_message
msgid "Import Message"
msgstr ""

#. module: base_import_module
#: model:ir.actions.act_window,name:base_import_module.action_view_base_module_import
#: model:ir.model,name:base_import_module.model_base_import_module
#: model:ir.ui.menu,name:base_import_module.menu_view_base_module_import
msgid "Import Module"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__with_demo
msgid "Import demo data of module"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_ir_module_module__imported
msgid "Imported Module"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields.selection,name:base_import_module.selection__ir_module_module__module_type__industries
msgid "Industries"
msgstr ""

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Install"
msgstr ""

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid "Install an Industry"
msgstr ""

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Install the application"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__write_uid
msgid "Last Updated by"
msgstr "इन्होंने आखिरी बार अपडेट किया"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__write_date
msgid "Last Updated on"
msgstr "आखिरी बार अपडेट हुआ"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Load demo data"
msgstr ""

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid ""
"Load demo data to test the industry's features with sample records. Do not "
"load them if this is your production database."
msgstr ""

#. module: base_import_module
#: model:ir.model,name:base_import_module.model_ir_module_module
msgid "Module"
msgstr "मॉड्यूल"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__module_file
msgid "Module .ZIP file"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_ir_module_module__module_type
msgid "Module Type"
msgstr ""

#. module: base_import_module
#: model:ir.model,name:base_import_module.model_base_module_uninstall
msgid "Module Uninstall"
msgstr ""

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Module file (.zip)"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__modules_dependencies
msgid "Modules Dependencies"
msgstr ""

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid "No file sent."
msgstr ""

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Note: you can only import data modules (.xml files and static assets)"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields.selection,name:base_import_module.selection__ir_module_module__module_type__official
msgid "Official Apps"
msgstr ""

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid "Only administrators can install data modules."
msgstr ""

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/controllers/main.py:0
msgid "Only administrators can upload a module"
msgstr ""

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid "Only zip files are supported."
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__state
msgid "Status"
msgstr "स्थिति"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid "Studio customizations require the Odoo Studio app."
msgstr ""

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid ""
"The installation of the data module would fail as the following dependencies"
" can't be found in the addons-path:\n"
msgstr ""

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid ""
"The list of industry applications cannot be fetched. Please try again later"
msgstr ""

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid "The module %s cannot be downloaded"
msgstr ""

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid "Unknown module dependencies:"
msgstr ""

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.module_form_apps_inherit
#: model_terms:ir.ui.view,arch_db:base_import_module.module_view_kanban_apps_inherit
msgid "Upgrade"
msgstr "अपग्रेड"

#. module: base_import_module
#: model:ir.model,name:base_import_module.model_ir_ui_view
msgid "View"
msgstr "देखें"

#. module: base_import_module
#: model:ir.model.fields.selection,name:base_import_module.selection__base_import_module__state__done
msgid "done"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields.selection,name:base_import_module.selection__base_import_module__state__init
msgid "init"
msgstr ""
