# The Invoice dict defines the structure of a UBL 2.1 Invoice, with all nodes in the correct order.
# It can be passed as the `template` argument to `dict_to_xml` to enforce the order of nodes.

import odoo.addons.account_edi_ubl_cii.tools.ubl_21_common as cac

InvoiceLine = {
    'cbc:ID': {},
    'cbc:UUID': {},
    'cbc:Note': {},
    'cbc:InvoicedQuantity': {},
    'cbc:LineExtensionAmount': {},
    'cbc:FreeOfChargeIndicator': {},
    'cac:InvoicePeriod': cac.Period,
    'cac:OrderLineReference': cac.OrderLineReference,
    'cac:BillingReference': cac.BillingReference,
    'cac:DocumentReference': cac.DocumentReference,
    'cac:PricingReference': cac.PricingReference,
    'cac:PaymentTerms': cac.PaymentTerms,
    'cac:AllowanceCharge': cac.AllowanceCharge,
    'cac:TaxTotal': cac.TaxTotal,
    'cac:WithholdingTaxTotal': cac.TaxTotal,
    'cac:Item': cac.Item,
    'cac:Price': cac.Price,
    'cac:ItemPriceExtension': cac.ItemPriceExtension,
}

Invoice = {
    '_tag': 'Invoice',
    'ext:UBLExtensions': {},
    'cbc:UBLVersionID': {},
    'cbc:CustomizationID': {},
    'cbc:ProfileID': {},
    'cbc:ProfileExecutionID': {},
    'cbc:ID': {},
    'cbc:CopyIndicator': {},
    'cbc:UUID': {},
    'cbc:IssueDate': {},
    'cbc:IssueTime': {},
    'cbc:DueDate': {},
    'cbc:InvoiceTypeCode': {},
    'cbc:Note': {},
    'cbc:DocumentCurrencyCode': {},
    'cbc:TaxCurrencyCode': {},
    'cbc:PricingCurrencyCode': {},
    'cbc:LineCountNumeric': {},
    'cbc:BuyerReference': {},
    'cac:InvoicePeriod': cac.Period,
    'cac:OrderReference': cac.OrderReference,
    'cac:BillingReference': cac.BillingReference,
    'cac:AdditionalDocumentReference': cac.DocumentReference,
    'cac:Signature': cac.Signature,
    'cac:AccountingSupplierParty': cac.SupplierParty,
    'cac:AccountingCustomerParty': cac.CustomerParty,
    'cac:SellerSupplierParty': cac.SupplierParty,
    'cac:Delivery': cac.Delivery,
    'cac:PaymentMeans': cac.PaymentMeans,
    'cac:PaymentTerms': cac.PaymentTerms,
    'cac:PrepaidPayment': cac.PrepaidPayment,
    'cac:AllowanceCharge': cac.AllowanceCharge,
    'cac:TaxExchangeRate': cac.ExchangeRate,
    'cac:PricingExchangeRate': cac.ExchangeRate,
    'cac:PaymentExchangeRate': cac.ExchangeRate,
    'cac:TaxTotal': cac.TaxTotal,
    'cac:WithholdingTaxTotal': cac.TaxTotal,
    'cac:LegalMonetaryTotal': cac.MonetaryTotal,
    'cac:InvoiceLine': InvoiceLine,
}
