# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* fleet
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# da<PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# NoaFarkash, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>el Nahmany, 2024
# MichaelHadar, 2024
# He<PERSON> <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# yael terner, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>rat <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# or balmas, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: or balmas, 2025\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_kanban
msgid "<i class=\"fa fa-map-marker\" title=\"Location\"/>"
msgstr "<i class=\"fa fa-map-marker\" title=\"Location\"/>"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
msgid ""
"<span class=\"o_stat_value\">New</span>\n"
"                                <span class=\"o_stat_text\">Vehicle</span>"
msgstr ""
"<span class=\"o_stat_value\">חדש</span>\n"
"                                    <span class=\"o_stat_text\">רכב</span>"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.res_config_settings_view_form
msgid "<span> days before the end date</span>"
msgstr "<span> ימים לפני תאריך הסיום</span>"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.res_config_settings_view_form
msgid "<span>Send an alert </span>"
msgstr "<span>שלח התראה </span>"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "<span>cm</span>"
msgstr "<span>ס\"מ</span>"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "<span>g/km</span>"
msgstr "<span>g/km</span>"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "<span>kW</span>"
msgstr "<span>קילוואט</span>"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "<span>km</span>"
msgstr "<span>ק\"מ</span>"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_1
msgid "A/C Compressor Replacement"
msgstr "החלפת מדחס מיזוג אוויר"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_2
msgid "A/C Condenser Replacement"
msgstr "החלפת מעבה מיזוג אוויר"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_3
msgid "A/C Diagnosis"
msgstr "אבחון מיזוג אוויר"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_4
msgid "A/C Evaporator Replacement"
msgstr "החלפת מאייד מיזוג אוויר"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_5
msgid "A/C Recharge"
msgstr "הטענת מיזוג אוויר"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_needaction
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_needaction
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_needaction
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__message_needaction
msgid "Action Needed"
msgstr "נדרשת פעולה"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Activation Cost"
msgstr "עלות הפעלה"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__active
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__active
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__active
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__active
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__active
msgid "Active"
msgstr "פעיל"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__activity_ids
msgid "Activities"
msgstr "פעילויות"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_exception_decoration
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_exception_decoration
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_exception_decoration
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "סימון פעילות חריגה"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_state
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_state
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_state
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__activity_state
msgid "Activity State"
msgstr "מצב פעילות"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_type_icon
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_type_icon
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_type_icon
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__activity_type_icon
msgid "Activity Type Icon"
msgstr "סוג פעילות"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.mail_activity_type_action_config_fleet
#: model:ir.ui.menu,name:fleet.fleet_menu_config_activity_type
msgid "Activity Types"
msgstr "סוגי פעילויות"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_tag_action
msgid "Add a new tag"
msgstr "הוסף תגית חדשה"

#. module: fleet
#: model:res.groups,name:fleet.fleet_group_manager
msgid "Administrator"
msgstr "מנהל מערכת"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_6
msgid "Air Filter Replacement"
msgstr "החלפת מסנן אוויר"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "All vehicles"
msgstr "כל הרכבים"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Apply New Driver"
msgstr "עדכון נהג חדש"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_kanban
msgid "Archive"
msgstr "ארכיון"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Archived"
msgstr "בארכיון"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__next_assignation_date
msgid "Assignment Date"
msgstr "תאריך שיוך"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__log_drivers
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_assignation_log_view_list
msgid "Assignment Logs"
msgstr "יומני מטלות"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_8
msgid "Assistance"
msgstr "עזרה"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_attachment_count
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_attachment_count
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_attachment_count
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__message_attachment_count
msgid "Attachment Count"
msgstr "כמות קבצים מצורפים"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_send_mail__attachment_ids
msgid "Attachments"
msgstr "קבצים מצורפים"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_kanban
msgid "Attention: renewal overdue"
msgstr "אזהרה: איחור בחידוש"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_send_mail__author_id
msgid "Author"
msgstr "מחבר"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__transmission__automatic
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__transmission__automatic
msgid "Automatic"
msgstr "אוטומטי"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Available"
msgstr "זמין"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__avatar_1920
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__avatar_1920
msgid "Avatar"
msgstr "אווטר"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__avatar_1024
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__avatar_1024
msgid "Avatar 1024"
msgstr "אווטר 1024"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__avatar_128
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__avatar_128
msgid "Avatar 128"
msgstr "אווטר 128"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__avatar_256
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__avatar_256
msgid "Avatar 256"
msgstr "אווטר 256"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__avatar_512
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__avatar_512
msgid "Avatar 512"
msgstr "אווטר 512"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_7
msgid "Ball Joint Replacement"
msgstr "החלפת ציר כדורי"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_9
msgid "Battery Inspection"
msgstr "בדיקת מצבר"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_10
msgid "Battery Replacement"
msgstr "החלפת מצבר"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_cost_report__vehicle_type__bike
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__vehicle_type__bike
msgid "Bike"
msgstr "אופניים"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__frame_type
msgid "Bike Frame Type"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Bikes"
msgstr "אופניים"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_send_mail__body_has_template_value
msgid "Body content is the same as the template"
msgstr "תוכן הגוף זהה לתבנית."

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_11
msgid "Brake Caliper Replacement"
msgstr "החלפת קליפר בלם"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_12
msgid "Brake Inspection"
msgstr "בדיקת בלמים"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_13
msgid "Brake Pad(s) Replacement"
msgstr "החלפת רפיד(ות) בלם"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__brand_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Brand"
msgstr "מותג"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_model_brand
msgid "Brand of the vehicle"
msgstr "מותג הרכב"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__cng
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__cng
msgid "CNG"
msgstr "מונע בגז"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__co2
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__default_co2
msgid "CO2 Emissions"
msgstr "פליטת פחמן דו חמצני"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_tree
msgid "CO2 Emissions g/km"
msgstr "פליטת פחמן דו-חמצני גרם לקילומטר g/Km"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__co2_standard
msgid "CO2 Standard"
msgstr "סטנדרט פחמן דו-חמצני"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__co2
msgid "CO2 emissions of the vehicle"
msgstr "פליטת פחמן דו חמצני של הרכב"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_1
msgid "Calculation Benefit In Kind"
msgstr "חישוב הכנסה בשווה-ערך"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_send_mail__can_edit_body
msgid "Can Edit Body"
msgstr "ניתן לערוך גוף"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_send_mail_view_form
msgid "Cancel"
msgstr "בטל"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__write_off_date
msgid "Cancellation Date"
msgstr "תאריך ביטול"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__state__closed
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_services__state__cancelled
msgid "Cancelled"
msgstr "בוטל"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_cost_report__vehicle_type__car
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__vehicle_type__car
msgid "Car"
msgstr "רכב"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_14
msgid "Car Wash"
msgstr "שטיפת מכוניות"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Cars"
msgstr "מכוניות"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__car_value
msgid "Catalog Value (VAT Incl.)"
msgstr "מחיר מחירון (כולל מע\"מ)"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_15
msgid "Catalytic Converter Replacement"
msgstr "החלפת ממיר קטליטי"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_model_category_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_model_category_menu
msgid "Categories"
msgstr "קטגוריות"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_model_category_action
msgid ""
"Categories will help you manage your fleet more efficiently and arrange your"
" vehicles."
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__category
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__category_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__category_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_search
msgid "Category"
msgstr "קטגוריה"

#. module: fleet
#: model:ir.model.constraint,message:fleet.constraint_fleet_vehicle_model_category_name_uniq
msgid "Category name must be unique"
msgstr "שם הקטגוריה צריך להיות ייחודי"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_model_category
msgid "Category of the model"
msgstr "קטגוריה של המודל"

#. module: fleet
#: model:mail.message.subtype,description:fleet.mt_fleet_driver_updated
#: model:mail.message.subtype,name:fleet.mt_fleet_driver_updated
msgid "Changed Driver"
msgstr "נהג שהוחלף"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_16
msgid "Charging System Diagnosis"
msgstr "אבחון מערכת טעינה"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__vin_sn
msgid "Chassis Number"
msgstr "מספר שלדה"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__state
msgid "Choose whether the contract is still valid or not"
msgstr "בחר אם החוזה עדיין תקף או לא"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_service_type__category
msgid ""
"Choose whether the service refer to contracts, vehicle services or both"
msgstr "בחר אם השירות מתייחס לחוזים, שירותי רכב או שניהם"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__contract_state__closed
msgid "Closed"
msgstr "סגור"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__co2_standard
msgid "Co2 Standard"
msgstr "סטנדרט פחמן דו-חמצני"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__color
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__color
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__color
msgid "Color"
msgstr "צבע"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__color
msgid "Color of the vehicle"
msgstr "צבע הרכב"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_send_mail_view_form
msgid "Communication about your vehicle."
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__company_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__company_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__company_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__company_id
msgid "Company"
msgstr "חברה"

#. module: fleet
#: model:ir.model,name:fleet.model_res_config_settings
msgid "Config Settings"
msgstr "הגדרות תצורה"

#. module: fleet
#: model:ir.ui.menu,name:fleet.fleet_configuration
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_kanban
msgid "Configuration"
msgstr "תצורה"

#. module: fleet
#: model:ir.model,name:fleet.model_res_partner
msgid "Contact"
msgstr "איש קשר"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_search
msgid "Contains Vehicle"
msgstr "כולל רכב"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_send_mail__body
msgid "Contents"
msgstr "תוכן"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_service_type__category__contract
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_cost_report__cost_type__contract
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Contract"
msgstr "חוזה"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_graph
msgid "Contract Costs Per Month"
msgstr "עלויות חוזה לחודש"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_count
msgid "Contract Count"
msgstr "כמות חוזים"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__expiration_date
msgid "Contract Expiration Date"
msgstr "תאריך תפוגת החוזה"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__start_date
msgid "Contract Start Date"
msgstr "תאריך התחלת חוזה"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_tree
msgid "Contract logs"
msgstr "יומני הסכמים"

#. module: fleet
#: model:mail.activity.type,name:fleet.mail_act_fleet_contract_to_renew
msgid "Contract to Renew"
msgstr "חוזה לחידוש"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_kanban
msgid "Contract(s)"
msgstr "חוזים"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_log_contract_action
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__log_contracts
#: model:ir.ui.menu,name:fleet.fleet_vehicle_log_contract_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Contracts"
msgstr "חוזים"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__cost
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__amount
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__amount
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Cost"
msgstr "עלות"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__cost_type
msgid "Cost Type"
msgstr "סוג עלות"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Cost that is paid only once at the creation of the contract"
msgstr "עלות שמשולמת רק פעם אחת עם יצירת החוזה"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__cost_subtype_id
msgid "Cost type purchased with this cost"
msgstr ""

#. module: fleet
#: model:ir.ui.menu,name:fleet.menu_fleet_reporting_costs
msgid "Costs"
msgstr "עלויות"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_costs_reporting_action
msgid "Costs Analysis"
msgstr "ניתוח עלויות"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__country_id
msgid "Country"
msgstr "ארץ"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__country_code
msgid "Country Code"
msgstr "קוד מדינה"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_model_category_action
msgid "Create a new category"
msgstr "יצירת קטגוריה חדשה"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_contract_action
msgid "Create a new contract"
msgstr "צור חוזה חדש"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_model_brand_action
msgid "Create a new manufacturer"
msgstr "צור יצרן חדש"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_model_action
msgid "Create a new model"
msgstr "צור דגם חדש"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_odometer_action
msgid "Create a new odometer log"
msgstr "צור רישום מד מרחק חדש"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_services_action
msgid "Create a new service entry"
msgstr "צור רשומת שירות חדשה"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_service_types_action
msgid "Create a new type of service"
msgstr "צור סוג חדש של שירות"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_state_action
msgid "Create a new vehicle status"
msgstr "צור סטטוס רכב חדש"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_send_mail__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_send_mail__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__currency_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__currency_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__currency_id
msgid "Currency"
msgstr "מטבע"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_assignation_log_view_list
msgid "Current Driver"
msgstr "נהג נוכחי"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__state_id
msgid "Current state of the vehicle"
msgstr "מצב נוכחי של הרכב"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__cost_frequency__daily
msgid "Daily"
msgstr "יומי"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__date_start
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__date
msgid "Date"
msgstr "תאריך"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__acquisition_date
msgid "Date of vehicle registration"
msgstr "תאריך רישום של הרכב"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__date
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__date
msgid "Date when the cost has been executed"
msgstr "תאריך בו העלות חושבה"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__start_date
msgid "Date when the coverage of the contract begins"
msgstr "תאריך תחילת הכיסוי של החוזה"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__expiration_date
msgid ""
"Date when the coverage of the contract expirates (by default, one year after"
" begin date)"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__write_off_date
msgid "Date when the vehicle's license plate has been cancelled/removed."
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_res_config_settings__delay_alert_contract
msgid "Delay alert contract outdated"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_kanban
msgid "Delete"
msgstr "מחיקה"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_2
msgid "Depreciation and Interests"
msgstr "פחת וריביות"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__description
msgid "Description"
msgstr "תיאור"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__frame_type__diamant
msgid "Diamant"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__diesel
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__diesel
msgid "Diesel"
msgstr "דיזל"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_send_mail__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_services__state__done
msgid "Done"
msgstr "בוצע"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_17
msgid "Door Window Motor/Regulator Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__doors
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__doors
msgid "Doors Number"
msgstr "מספר דלתות"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_downgraded
msgid "Downgraded"
msgstr "שונמך"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__driver_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__driver_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__driver_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__purchaser_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__purchaser_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__driver_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Driver"
msgstr "נהג"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__driver_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__purchaser_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_odometer__driver_id
msgid "Driver address of the vehicle"
msgstr "כתובת נהג הרכב"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Drivers"
msgstr "נהגים"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Drivers History"
msgstr "היסטוריית הנהגים"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__history_count
msgid "Drivers History Count"
msgstr ""

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_assignation_log
msgid "Drivers history on a vehicle"
msgstr "היסטוריית נהגים ברכב"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_contract_action
msgid ""
"Each contract (e.g.: leasing) may include several services\n"
"            (reparation, insurances, periodic maintenance)."
msgstr ""
"כל חוזה (למשל: ליסינג) עשוי לכלול מספר שירותים\n"
"            (תיקונים, ביטוחים, תחזוקה תקופתית).   "

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_service_types_action
msgid "Each service can used in contracts, as a standalone service or both."
msgstr "כל שירות יכול להיות בשימוש בחוזים, כשירות עצמאי או בשניהם."

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__electric
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__electric
msgid "Electric"
msgstr "חשמלי"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__electric_assistance
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__electric_assistance
msgid "Electric Assistance"
msgstr "סיוע אלקטרוני"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_16
msgid "Emissions"
msgstr "פליטות"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_leasing
msgid "Employee Car"
msgstr "רכב עובד"

#. module: fleet
#. odoo-python
#: code:addons/fleet/models/fleet_vehicle_log_services.py:0
msgid "Emptying the odometer value of a vehicle is not allowed."
msgstr "אסור לאפס את ערך מד מרחק הרכב."

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__date_end
msgid "End Date"
msgstr "תאריך סיום"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.res_config_settings_view_form
msgid "End Date Contract Alert"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Engine"
msgstr "מנוע"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_18
msgid "Engine Belt Inspection"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_19
msgid "Engine Coolant Replacement"
msgstr "החלפת נוזל קירור למנוע"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_20
msgid "Engine/Drive Belt(s) Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_12
msgid "Entry into service tax"
msgstr ""

#. module: fleet
#. odoo-javascript
#: code:addons/fleet/static/src/js/fleet_form.js:0
msgid ""
"Every service and contract of this vehicle will be considered as archived. "
"Are you sure that you want to archive this record?"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_21
msgid "Exhaust Manifold Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__contract_state__expired
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__state__expired
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
msgid "Expired"
msgstr "פג תוקף"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__expires_today
msgid "Expires Today"
msgstr "פג תוקף היום"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__first_contract_date
msgid "First Contract Date"
msgstr "תאריך תחילת חוזה"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Fiscality"
msgstr "כספי"

#. module: fleet
#: model:ir.module.category,name:fleet.module_fleet_category
#: model:ir.ui.menu,name:fleet.fleet_vehicle_menu
#: model:ir.ui.menu,name:fleet.fleet_vehicles
#: model:ir.ui.menu,name:fleet.menu_root
#: model_terms:ir.ui.view,arch_db:fleet.res_config_settings_view_form
msgid "Fleet"
msgstr "צי רכב"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_cost_report
msgid "Fleet Analysis Report"
msgstr "דוח ניתוח צי"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_graph
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vechicle_costs_report_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vechicle_costs_report_view_tree
msgid "Fleet Costs Analysis"
msgstr "ניתוח עלויות צי"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.res_config_settings_view_form
msgid "Fleet Management"
msgstr "ניהול צי רכב"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__manager_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__manager_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_search
msgid "Fleet Manager"
msgstr "מנהל צי רכב"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_service_type
msgid "Fleet Service Type"
msgstr "סוג שירות צי"

#. module: fleet
#: model:ir.actions.server,name:fleet.ir_cron_contract_costs_generator_ir_actions_server
msgid "Fleet: Generate contracts costs based on costs frequency"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_follower_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_follower_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_follower_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__message_follower_ids
msgid "Followers"
msgstr "עוקבים"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_partner_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_partner_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_partner_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__message_partner_ids
msgid "Followers (Partners)"
msgstr "עוקבים (לקוחות/ספקים)"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__activity_type_icon
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__activity_type_icon
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__activity_type_icon
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_model__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "פונט מדהים למשל עבור משימות fa-tasks"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__frame_size
msgid "Frame Size"
msgstr "גודל השלדה"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__fuel_type
msgid "Fuel"
msgstr "דלק"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_22
msgid "Fuel Injector Replacement"
msgstr "החלפת מזרק דלק"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_23
msgid "Fuel Pump Replacement"
msgstr "החלפת משאבת דלק"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__fuel_type
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__default_fuel_type
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Fuel Type"
msgstr "סוג דלק"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__full_hybrid
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__full_hybrid
msgid "Full Hybrid"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Future Activities"
msgstr "פעילויות עתידיות"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__future_driver_id
msgid "Future Driver"
msgstr "נהג עתידי"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_kanban
msgid "Future Driver :"
msgstr "נהג עתידי :"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__gasoline
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__gasoline
msgid "Gasoline"
msgstr "בנזין"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_service_types_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Group By"
msgstr "קבץ לפי"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_renewal_overdue
msgid "Has Contracts Overdue"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_renewal_due_soon
msgid "Has Contracts to renew"
msgstr "יש הסכמים לחדש"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__has_message
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__has_message
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__has_message
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__has_message
msgid "Has Message"
msgstr "יש הודעה"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__has_open_contract
msgid "Has Open Contract"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_24
msgid "Head Gasket(s) Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_25
msgid "Heater Blower Motor Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_26
msgid "Heater Control Valve Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_27
msgid "Heater Core Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_28
msgid "Heater Hose Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__horsepower
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__horsepower
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__power_unit__horsepower
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__power_unit__horsepower
msgid "Horsepower"
msgstr "כוח סוס"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__horsepower_tax
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__horsepower_tax
msgid "Horsepower Taxation"
msgstr "מס כוח סוס"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__hydrogen
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__hydrogen
msgid "Hydrogen"
msgstr "מימן"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_send_mail__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__id
msgid "ID"
msgstr "מזהה"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_exception_icon
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_exception_icon
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_exception_icon
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__activity_exception_icon
msgid "Icon"
msgstr "סמל"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__activity_exception_icon
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__activity_exception_icon
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__activity_exception_icon
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_model__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "סמל לציון פעילות חריגה."

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__message_needaction
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__message_needaction
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__message_needaction
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_model__message_needaction
msgid "If checked, new messages require your attention."
msgstr "אם מסומן, הודעות חדשות דורשות את תשומת לבך."

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__message_has_error
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__message_has_error
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__message_has_error
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_model__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "אם מסומן, בחלק מההודעות קיימת שגיאת משלוח."

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_29
msgid "Ignition Coil Replacement"
msgstr "החלפת סליל מצת"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__image_1920
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__image_1920
msgid "Image"
msgstr "תמונה"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__image_1024
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__image_1024
msgid "Image 1024"
msgstr "תמונה 1024"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__image_128
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__image_128
msgid "Image 128"
msgstr "תמונה 128"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__image_256
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__image_256
msgid "Image 256"
msgstr "תמונה 256"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__image_512
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__image_512
msgid "Image 512"
msgstr "תמונה 512"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__contract_state__open
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
msgid "In Progress"
msgstr "בתהליך"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__service_ids
msgid "Included Services"
msgstr "שירותים כלולים"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__contract_state__futur
msgid "Incoming"
msgstr "נכנס"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
msgid "Information"
msgstr "מידע"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_30
msgid "Intake Manifold Gasket Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_send_mail__is_mail_template_editor
msgid "Is Editor"
msgstr "האם עורך"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_is_follower
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_is_follower
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_is_follower
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__message_is_follower
msgid "Is Follower"
msgstr "עוקב"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_junior
msgid "Junior"
msgstr "ג'וניור"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_kanban
msgid "Km"
msgstr "ק\"מ"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__lpg
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__lpg
msgid "LPG"
msgstr "גפ\"מ"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_send_mail__lang
msgid "Language"
msgstr "שפה"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_state
msgid "Last Contract State"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__odometer
msgid "Last Odometer"
msgstr "מד מרחק אחרון"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_send_mail__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_send_mail__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Late Activities"
msgstr "פעילויות באיחור"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_contract_leasing
msgid "Leasing"
msgstr "ליסינג"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_action
msgid "Let's create your first vehicle."
msgstr "בואו ניצור את הרכב הראשון שלך."

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__license_plate
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "License Plate"
msgstr "מספר רישוי"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__license_plate
msgid "License plate number of the vehicle (i = plate number for a car)"
msgstr "לוח רישוי של הרכב (i=מספר לוחית הזיהוי של הרכב)"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_send_mail_view_form
msgid "Load template"
msgstr "טען תבנית"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__location
msgid "Location"
msgstr "מיקום"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__location
msgid "Location of the vehicle (garage, ...)"
msgstr "מיקום הרכב (מוסך, ...)"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__image_128
msgid "Logo"
msgstr "לוגו"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_kanban
msgid "MODELS"
msgstr "דגמים"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_send_mail__template_id
msgid "Mail Template"
msgstr "תבנית דוא\"ל"

#. module: fleet
#: model:ir.actions.server,name:fleet.action_fleet_vehicle_send_mail
msgid "Mail to Driver"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_contract_action
msgid ""
"Manage all your contracts (leasing, insurances, etc.) with\n"
"            their related services, costs. Odoo will automatically warn\n"
"            you when some contracts have to be renewed."
msgstr ""
"נהל את כל החוזים שלך (ליסינג, ביטוחים וכו') עם\n"
"            השירותים הקשורים להם, עלויות. Odoo יתריע אוטומטית\n"
"            כשתצטרך לחדש חוזים מסוימים."

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_costs_reporting_action
msgid "Manage efficiently your different effective vehicles Costs with Odoo."
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_10
msgid "Management Fee"
msgstr "עלויות ניהול"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__transmission__manual
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__transmission__manual
msgid "Manual"
msgstr "ידני"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__brand_id
msgid "Manufacturer"
msgstr "יצרן"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_model_brand_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_model_brand_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_search
msgid "Manufacturers"
msgstr "יצרנים"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_has_error
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_has_error
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_has_error
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__message_has_error
msgid "Message Delivery error"
msgstr "הודעת שגיאת שליחה"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__message_ids
msgid "Messages"
msgstr "הודעות"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__model_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__model_ids
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Model"
msgstr "מודל"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_category_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_category_view_tree
msgid "Model Category"
msgstr "קטגוריית דגם"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__model_count
msgid "Model Count"
msgstr "מספר דגמים"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_tree
msgid "Model Make"
msgstr "דגם יצרן"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__model_year
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__model_year
msgid "Model Year"
msgstr "שנת דגם"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__name
msgid "Model name"
msgstr "שם דגם"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_model
msgid "Model of a vehicle"
msgstr "דגם הרכב"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_model_action
#: model:ir.ui.menu,name:fleet.fleet_models_configuration
#: model:ir.ui.menu,name:fleet.fleet_vehicle_model_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_tree
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_kanban
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_tree
msgid "Models"
msgstr "מודלים"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__cost_frequency__monthly
msgid "Monthly"
msgstr "חודשי"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__my_activity_date_deadline
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__my_activity_date_deadline
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__my_activity_date_deadline
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "מועד אחרון לפעילות שלי"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__name
msgid "Name"
msgstr "שם"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Need Action"
msgstr "נדרשת פעולה"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__state__futur
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_services__state__new
msgid "New"
msgstr "חדש"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_new_request
msgid "New Request"
msgstr "בקשה חדשה"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_calendar_event_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_calendar_event_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_calendar_event_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "הפעילות הבאה ביומן"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_date_deadline
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_date_deadline
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_date_deadline
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "מועד אחרון לפעילות הבאה"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_summary
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_summary
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_summary
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__activity_summary
msgid "Next Activity Summary"
msgstr "תיאור הפעילות הבאה "

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_type_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_type_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_type_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__activity_type_id
msgid "Next Activity Type"
msgstr "סוג הפעילות הבאה"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__future_driver_id
msgid "Next Driver Address of the vehicle"
msgstr "כתובת הנהג החדש ברכב"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__cost_frequency__no
msgid "No"
msgstr "לא"

#. module: fleet
#. odoo-python
#: code:addons/fleet/models/fleet_vehicle.py:0
msgid "No Plate"
msgstr "ללא לוחית"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_costs_reporting_action
msgid "No data for analysis"
msgstr "אין נתונים לניתוח"

#. module: fleet
#. odoo-python
#: code:addons/fleet/models/fleet_vehicle.py:0
msgid "No plate"
msgstr "ללא לוחית"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__service_activity__none
msgid "None"
msgstr "אף אחד"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Note"
msgstr "הערה"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__notes
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
msgid "Notes"
msgstr "הערות"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_needaction_counter
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_needaction_counter
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_needaction_counter
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__message_needaction_counter
msgid "Number of Actions"
msgstr "מספר פעולות"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__doors
msgid "Number of doors of the vehicle"
msgstr "מספר דלתות ברכב"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_has_error_counter
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_has_error_counter
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_has_error_counter
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__message_has_error_counter
msgid "Number of errors"
msgstr "מספר השגיאות"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__message_needaction_counter
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__message_needaction_counter
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__message_needaction_counter
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_model__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "מספר הודעות הדורשות פעולה"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__message_has_error_counter
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__message_has_error_counter
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__message_has_error_counter
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_model__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "מספר הודעות עם שגיאת משלוח"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__seats
msgid "Number of seats of the vehicle"
msgstr "מספר מושבים ברכב"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__odometer_count
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__odometer_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Odometer"
msgstr "מד מרחק"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_tree
msgid "Odometer Logs"
msgstr "רישומי מד מרחק"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__odometer_unit
msgid "Odometer Unit"
msgstr "יחידת מד מרחק"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__odometer
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__value
msgid "Odometer Value"
msgstr "ערך מד מרחק"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_graph
msgid "Odometer Values Per Vehicle"
msgstr "ערכי מד מרחק לרכב"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_odometer
msgid "Odometer log for a vehicle"
msgstr "רישום מד מרחק לרכב"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__odometer
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__odometer
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__odometer_id
msgid "Odometer measure of the vehicle at the moment of this log"
msgstr "מד מרחק לרכב בעת רישום זה"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_odometer_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_odometer_menu
msgid "Odometers"
msgstr ""

#. module: fleet
#: model:res.groups,name:fleet.fleet_group_user
msgid "Officer: Manage all vehicles"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_31
msgid "Oil Change"
msgstr "החלפת שמן"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_32
msgid "Oil Pump Replacement"
msgstr "החלפת משאבת שמן"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_contract_omnium
msgid "Omnium"
msgstr ""

#. module: fleet
#. odoo-python
#: code:addons/fleet/models/fleet_vehicle_model.py:0
msgid "Operation not supported."
msgstr "הפעולה אינה נתמכת."

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_send_mail__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"שפת תרגום אופציונלית (קוד ISO) לבחירה בעת שליחת אימייל. אם לא מוגדר, הגרסה "
"האנגלית תשמש. בדרך כלל זה צריך להיות ביטוי מציין מיקום המספק את השפה "
"המתאימה, למשל. {{ object.partner_id.lang }}."

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_15
msgid "Options"
msgstr "מוצרים אופציונליים"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__order_date
msgid "Order Date"
msgstr "תאריך הזמנה"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_ordered
msgid "Ordered"
msgstr "הוזמן"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_33
msgid "Other Maintenance"
msgstr "תחזוקה אחרת"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__service_activity__overdue
msgid "Overdue"
msgstr "איחור"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_34
msgid "Oxygen Sensor Replacement"
msgstr "החלפת חיישן חמצן"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__plan_to_change_bike
#: model:ir.model.fields,field_description:fleet.field_res_partner__plan_to_change_bike
#: model:ir.model.fields,field_description:fleet.field_res_users__plan_to_change_bike
msgid "Plan To Change Bike"
msgstr "תכנית להחליף אופניים"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__plan_to_change_car
#: model:ir.model.fields,field_description:fleet.field_res_partner__plan_to_change_car
#: model:ir.model.fields,field_description:fleet.field_res_users__plan_to_change_car
msgid "Plan To Change Car"
msgstr "מתכנן להחליף רכב"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Planned for Change"
msgstr "מתוכנן לשינוי"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__plug_in_hybrid_diesel
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__plug_in_hybrid_diesel
msgid "Plug-in Hybrid Diesel"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__plug_in_hybrid_gasoline
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__plug_in_hybrid_gasoline
msgid "Plug-in Hybrid Gasoline"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__power
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__power
msgid "Power"
msgstr "הספק"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_35
msgid "Power Steering Hose Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_36
msgid "Power Steering Pump Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__power_unit
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__power_unit
msgid "Power Unit"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__power
msgid "Power in kW of the vehicle"
msgstr "הספק בקילו וואט של הרכב"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__vehicle_properties
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Properties"
msgstr "מאפיינים"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__net_car_value
msgid "Purchase Value"
msgstr "מחיר רכישה"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_purchased
msgid "Purchased"
msgstr "נרכש"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_37
msgid "Radiator Repair"
msgstr "תיקון רדיאטור"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__vehicle_range
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__vehicle_range
msgid "Range"
msgstr "טווח"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_action
msgid "Ready to manage your fleet more efficiently?"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__cost_generated
msgid "Recurring Cost"
msgstr "עלות חוזרת"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__cost_frequency
msgid "Recurring Cost Frequency"
msgstr "תדירות עלות חוזרת"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__ins_ref
msgid "Reference"
msgstr "מזהה"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_refueling
msgid "Refueling"
msgstr "מילוי דלק"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_registered
msgid "Registered"
msgstr "רשומים"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__acquisition_date
msgid "Registration Date"
msgstr "תאריך הרשמה"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_send_mail__render_model
msgid "Rendering Model"
msgstr "דגם עיבוד"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_11
msgid "Rent (Excluding VAT)"
msgstr "שכירות (בלי מע\"מ)"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_7
msgid "Repair and maintenance"
msgstr "תיקון ותחזוקה"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_contract_repairing
msgid "Repairing"
msgstr "תיקונים"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_9
msgid "Replacement Vehicle"
msgstr "רכב חלופי"

#. module: fleet
#: model:ir.ui.menu,name:fleet.menu_fleet_reporting
msgid "Reporting"
msgstr "דו\"חות"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_reserve
msgid "Reserve"
msgstr "שמירה"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__residual_value
msgid "Residual Value"
msgstr "סכום להכרה"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_14
msgid "Residual value (Excluding VAT)"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_18
msgid "Residual value in %"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__user_id
msgid "Responsible"
msgstr "אחראי"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_user_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_user_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_user_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__activity_user_id
msgid "Responsible User"
msgstr "משתמש אחראי"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_kanban
msgid "Restore"
msgstr "שחזר"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_38
msgid "Resurface Rotors"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_39
msgid "Rotate Tires"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_40
msgid "Rotor Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__state__open
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_services__state__running
msgid "Running"
msgstr "רץ"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_send_mail_view_form
msgid "Save as new template"
msgstr "שמור כתבנית חדשה"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__seats
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__seats
msgid "Seats Number"
msgstr "מספר מושבים"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_send_mail_view_form
msgid "Send"
msgstr "שלח"

#. module: fleet
#. odoo-python
#: code:addons/fleet/models/fleet_vehicle.py:0
msgid "Send Email"
msgstr "שלח דוא\"ל"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_send_mail
msgid "Send mails to Drivers"
msgstr ""

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_senior
msgid "Senior"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__sequence
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__sequence
msgid "Sequence"
msgstr "רצף"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_service_type__category__service
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_cost_report__cost_type__service
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_search
msgid "Service"
msgstr "שירות"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__service_activity
msgid "Service Activity"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__service_type_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_search
msgid "Service Type"
msgstr "סוג שירות"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_service_types_view_tree
msgid "Service Types"
msgstr "סוגי שירותים"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_log_services_action
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__service_count
#: model:ir.ui.menu,name:fleet.fleet_services_configuration
#: model:ir.ui.menu,name:fleet.fleet_vehicle_log_services_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_activity
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Services"
msgstr "שירותים"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_graph
msgid "Services Costs Per Month"
msgstr "עלויות שירות לחודש"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__log_services
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_tree
msgid "Services Logs"
msgstr "רישומי שירותים "

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_log_services
msgid "Services for vehicles"
msgstr "שירות למכוניות"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_config_settings_action
#: model:ir.ui.menu,name:fleet.fleet_config_settings_menu
msgid "Settings"
msgstr "הגדרות"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Show all records which has next action date is before today"
msgstr "הצג את כל הרשומות שתאריך הפעולה הבא שלהן הוא עד היום"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_6
msgid "Snow tires"
msgstr "גלגלי שלג"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_41
msgid "Spark Plug Replacement"
msgstr "החלפת מצת התנעה"

#. module: fleet
#. odoo-python
#: code:addons/fleet/models/fleet_vehicle.py:0
msgid "Specify the End date of %s"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__state
msgid "Stage"
msgstr "שלב"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__date_start
msgid "Start Date"
msgstr "תאריך תחילה"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_42
msgid "Starter Replacement"
msgstr "החלפת מצת התנעה"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__state_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_state_view_tree
msgid "State"
msgstr "סטטוס"

#. module: fleet
#: model:ir.model.constraint,message:fleet.constraint_fleet_vehicle_state_fleet_state_name_unique
msgid "State name already exists"
msgstr "שם המצב כבר קיים"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_state_action
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__state
#: model:ir.ui.menu,name:fleet.fleet_vehicle_state_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Status"
msgstr "סטטוס"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__activity_state
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__activity_state
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__activity_state
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_model__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"סטטוס על בסיס פעילויות\n"
"איחור: תאריך היעד כבר חלף\n"
"היום: תאריך הפעילות הוא היום\n"
"מתוכנן: פעילויות עתידיות."

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_send_mail__subject
msgid "Subject"
msgstr "נושא"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vechicle_costs_report_view_tree
msgid "Sum of Cost"
msgstr "סך עלויות"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_5
msgid "Summer tires"
msgstr "צמיגי קיץ"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__name
msgid "Tag Name"
msgstr "שם תגית"

#. module: fleet
#: model:ir.model.constraint,message:fleet.constraint_fleet_vehicle_tag_name_uniq
msgid "Tag name already exists!"
msgstr "שם התגית כבר קיים!"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_tag_action
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__tag_ids
#: model:ir.ui.menu,name:fleet.fleet_vehicle_tag_menu
msgid "Tags"
msgstr "תגיות"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Tax Info"
msgstr "מידע על המס"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_3
msgid "Tax roll"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__notes
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Terms and Conditions"
msgstr "תנאים והגבלות"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"קוד ארץ ISO בשני תווים. \n"
"ניתן להשתמש בשדה זה לחיפוש מהיר."

#. module: fleet
#. odoo-python
#: code:addons/fleet/wizard/fleet_vehicle_send_mail.py:0
msgid "The following vehicle drivers are missing an email address: %s."
msgstr ""

#. module: fleet
#. odoo-python
#: code:addons/fleet/models/fleet_vehicle.py:0
msgid "The odometer value cannot be lower than the previous one."
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_43
msgid "Thermostat Replacement"
msgstr "החלפת מדחום"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__next_assignation_date
msgid ""
"This is the date at which the car will be available, if not set it means "
"available instantly"
msgstr "זה התאריך ממנו הרכב יהיה זמין. אם ריק הרכב יהיה זמין מיד."

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_44
msgid "Tie Rod End Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_45
msgid "Tire Replacement"
msgstr "החלפת צמיגים"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_46
msgid "Tire Service"
msgstr "שירות צמיגים"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_to_order
msgid "To Order"
msgstr "להזמין"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__service_activity__today
msgid "Today"
msgstr "היום"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Today Activities"
msgstr "פעילויות היום"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_tree
msgid "Total"
msgstr "סה\"כ"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_13
msgid "Total expenses (Excluding VAT)"
msgstr "סהכ הוצאות (ללא מע\"מ)"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_17
msgid "Touring Assistance"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_services_action
msgid ""
"Track all the services done on your vehicle.\n"
"            Services can be of many types: occasional repair, fixed maintenance, etc."
msgstr ""
"עקוב אחר כל השירותים שבוצעו עבור הרכב שלך.\n"
"            השירותים יכולים להיות מסוגים רבים: תיקון מדי פעם, תחזוקה קבועה וכו'."

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__trailer_hook
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__trailer_hook
msgid "Trailer Hitch"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Trailer Hook"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__transmission
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__transmission
msgid "Transmission"
msgstr "תיבת הילוכים"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_47
msgid "Transmission Filter Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_48
msgid "Transmission Fluid Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_49
msgid "Transmission Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__frame_type__trapez
msgid "Trapez"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__cost_subtype_id
msgid "Type"
msgstr "סוג"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__activity_exception_decoration
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__activity_exception_decoration
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__activity_exception_decoration
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_model__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "סוג הפעילות החריגה ברשומה."

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_service_types_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_service_types_menu
msgid "Types"
msgstr "סוגים"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__vin_sn
msgid "Unique number written on the vehicle motor (VIN/SN number)"
msgstr "מספר ייחודי רשום על מנוע הרחב (מספר VIN/SN)"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__odometer_unit
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__unit
msgid "Unit"
msgstr "יחידה"

#. module: fleet
#. odoo-python
#: code:addons/fleet/models/fleet_vehicle_model.py:0
#: model:ir.model,name:fleet.model_fleet_vehicle
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__vehicle_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__vehicle_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__vehicle_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__vehicle_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__vehicle_id
#: model:ir.ui.menu,name:fleet.fleet_vehicles_configuration
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_tree
msgid "Vehicle"
msgstr "רכב"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_log_contract
msgid "Vehicle Contract"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__vehicle_count
msgid "Vehicle Count"
msgstr "מספר רכבים"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__description
msgid "Vehicle Description"
msgstr "תיאור רכב"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
msgid "Vehicle Information"
msgstr "פרטי רכב"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__name
msgid "Vehicle Name"
msgstr "שם הרכב"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__vehicle_properties_definition
msgid "Vehicle Properties"
msgstr ""

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_state
msgid "Vehicle Status"
msgstr "סטטוס רכב"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_tag
msgid "Vehicle Tag"
msgstr "תגית רכב"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_tag_view_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_tag_view_view_tree
msgid "Vehicle Tags"
msgstr "תגיות רכבים"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__vehicle_type
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__vehicle_type
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__vehicle_type
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_search
msgid "Vehicle Type"
msgstr "סוג הרכב"

#. module: fleet
#. odoo-python
#: code:addons/fleet/wizard/fleet_vehicle_send_mail.py:0
msgid "Vehicle: Mass mail drivers"
msgstr ""

#. module: fleet
#. odoo-python
#: code:addons/fleet/models/fleet_vehicle_model.py:0
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_action
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_send_mail__vehicle_ids
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_tree
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_activity
msgid "Vehicles"
msgstr "רכבים"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_activity
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
msgid "Vehicles Contracts"
msgstr "חוזי רכבים"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_search
msgid "Vehicles costs"
msgstr "עלויות רכבים"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_search
msgid "Vehicles odometers"
msgstr "מדי מרחק של רכבים"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__insurer_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__vendor_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
msgid "Vendor"
msgstr "ספק"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__inv_ref
msgid "Vendor Reference"
msgstr "מספר הזמנת ספק"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__vendors
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
msgid "Vendors"
msgstr "ספקים"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_waiting_list
msgid "Waiting List"
msgstr "רשימת המתנה"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__days_left
msgid "Warning Date"
msgstr "תאריך אזהרה"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_kanban
msgid "Warning: renewal due soon"
msgstr "אזהרה: חידוש צפוי בקרוב"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_50
msgid "Water Pump Replacement"
msgstr "החלפת משאבת מים"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__frame_type__wave
msgid "Wave"
msgstr "גלי"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__cost_frequency__weekly
msgid "Weekly"
msgstr "שבועי"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_51
msgid "Wheel Alignment"
msgstr "כיוון פרונט וגלגלים"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_52
msgid "Wheel Bearing Replacement"
msgstr "החלפת מיסבים"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_53
msgid "Windshield Wiper(s) Replacement"
msgstr "החלפת מגבי שמשה קדמית"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_search
msgid "With Models"
msgstr "עם הדגמים"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Write here all other information relative to this contract"
msgstr "כתוב כאן את כל המידע הנוסף הקשור לחוזה זה"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
msgid "Write here any other information related to the service completed."
msgstr "כתוב כאן כל מידע אחר הקשור לשירות שהושלם."

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Write here any other information related to this vehicle"
msgstr "כתבו כאן כל מידע נוסף הרלוונטי לרכב"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_send_mail_view_form
msgid "Write your message here..."
msgstr "כתיבת הודעה כאן..."

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__model_year
msgid "Year of the model"
msgstr "שנת הדגם"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__cost_frequency__yearly
msgid "Yearly"
msgstr "שנתי"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_odometer_action
msgid "You can add various odometer entries for all vehicles."
msgstr "אתה יכול להוסיף ערכי מד מרחק שונים לכל הרכבים."

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_state_action
msgid ""
"You can customize available status to track the evolution of\n"
"            each vehicle. Example: active, being repaired, sold."
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_model_action
msgid "You can define several models (e.g. A3, A4) for each make (Audi)."
msgstr "ניתן להגדיר מספר דגמים (למשל A3, A4) לכל יצרנית (אאודי)."

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "e.g. Model S"
msgstr "למשל: דגם S"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "e.g. PAE 326"
msgstr "למשל: PAE 326"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
msgid "e.g. Tesla"
msgstr "למשל: טסלה"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__power_unit__power
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__power_unit__power
msgid "kW"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__odometer_unit__kilometers
msgid "km"
msgstr "ק\"מ"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__odometer_unit__miles
msgid "mi"
msgstr "mi"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "show the contract for this vehicle"
msgstr "הצג את החוזה לרכב זה"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "show the odometer logs for this vehicle"
msgstr "הצג את רישומי מד המרחק לרכב זה"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "show the services logs for this vehicle"
msgstr "הצג את רישומי השירותים לרכב זה"
