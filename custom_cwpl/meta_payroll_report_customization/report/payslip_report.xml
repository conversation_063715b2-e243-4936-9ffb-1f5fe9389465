<?xml version="1.0" encoding="utf-8"?>
<odoo>


    <template id="report_payslip_document_custom" inherit_id="hr_payroll.report_payslip">
        <xpath expr="//div[@class='page']/h2" position="attributes">
            <attribute name="style">display: none;</attribute>
        </xpath>
        <xpath expr="//div[@class='page']/h2" position="attributes">
            <attribute name="style">display: none;</attribute>
        </xpath>

        <xpath expr="//table[@name='employee-infos']" position="attributes">
            <attribute name="style">display: none;</attribute>
        </xpath>
        <xpath expr="//table[2]" position="attributes">
            <attribute name="style">display: none;</attribute>
        </xpath>
        <xpath expr="//div[@id='total']" position="attributes">
            <attribute name="style">display: none;</attribute>
        </xpath>
        <xpath expr="//t[@t-if='is_invalid']" position="attributes">
            <attribute name="style">display: none;</attribute>
        </xpath>

        <xpath expr="//div[@class='page']/h2" position="after">
            <div style="margin-bottom: 20px;">
                <strong style="font-size: 18px;">Salary Slip of</strong>
                <strong style="font-size: 18px;" t-field="o.employee_id.name_for_pay_slip"/>
 -                <strong style="font-size: 18px;" t-field="o.employee_id.barcode"/>
            </div>
        </xpath>
        <xpath expr="//table[@name='employee-infos']" position="after">
            <table class="table table-sm table-bordered" style="margin-bottom: 50px; border: 1px solid black !important;">
                <tr style="border: 1px solid black !important;">
                    <td class="text-center" width="25%" style="border: 1px solid black !important;">
                        <strong>Employee</strong>
                    </td>
                    <td class="text-center" width="25%" style="border: 1px solid black !important;">
                        <strong>Department</strong>
                    </td>
                    <td class="text-center" width="25%" style="border: 1px solid black !important;">
                        <strong>Designation</strong>
                    </td>
                    <td class="text-center" width="25%" style="border: 1px solid black !important;">
                        <strong>Pay Period</strong>
                    </td>
                </tr>
                <tr style="border: 1px solid black !important;">
                    <td class="text-center" style="border: 1px solid black !important;">
                        <span t-field="o.employee_id.name_for_pay_slip"/>
                    </td>
                    <td class="text-center" style="border: 1px solid black !important;">
                        <span t-field="o.employee_id.department_id"/>
                    </td>
                    <td class="text-center" style="border: 1px solid black !important;">
                        <span t-field="o.employee_id.job_title"/>
                    </td>
                    <td class="text-center" style="border: 1px solid black !important;">
                        <span t-field="o.date_from"/>
 to                        <span t-field="o.date_to"/>
                    </td>
                </tr>
                <tr style="border: 1px solid black !important;">
                    <td class="text-center" width="25%" style="border: 1px solid black !important;">
                        <strong>Email</strong>
                    </td>
                    <td class="text-center" width="25%" style="border: 1px solid black !important;">
                        <strong>Person in charge </strong>
                    </td>
                    <td class="text-center" width="25%" style="border: 1px solid black !important;">
                        <strong>Identification</strong>
                    </td>
                    <td class="text-center" width="25%" style="border: 1px solid black !important;">
                        <strong>Computed on</strong>
                    </td>
                </tr>
                <tr style="border: 1px solid black !important;">
                    <td class="text-center" style="border: 1px solid black !important;">
                        <span t-field="o.employee_id.work_email"/>
                    </td>
                    <td class="text-center" style="border: 1px solid black !important;">
                        <span t-field="o.employee_id.children"/>
                    </td>
                    <td class="text-center" style="border: 1px solid black !important;">
                        <span t-field="o.employee_id.identification_id"/>
                    </td>
                    <td class="text-center" style="border: 1px solid black !important;">
                        <span t-field="o.compute_date"/>
                    </td>
                </tr>
                <tr style="border: 1px solid black !important;">
                    <td class="text-center" width="25%" style="border: 1px solid black !important;">
                        <strong>Joining Date</strong>
                    </td>
                    <td class="text-center" width="25%" style="border: 1px solid black !important;">
                        <strong>Contract Type</strong>
                    </td>
                    <td class="text-center" width="25%" style="border: 1px solid black !important;">
                        <strong>Working Schedule</strong>
                    </td>
                    <td class="text-center" width="25%" style="border: 1px solid black !important;">
                        <strong>Location</strong>
                    </td>
                </tr>
                <tr style="border: 1px solid black !important;">
                    <td class="text-center" style="border: 1px solid black !important;">
                        <span t-field="o.employee_id.first_contract_date"/>
                    </td>
                    <td class="text-center" style="border: 1px solid black !important;">
                        <span t-field="o.employee_id.contract_id.contract_type_id"/>
                    </td>
                    <td class="text-center" style="border: 1px solid black !important;">
                        <span t-field="o.employee_id.contract_id.hours_per_week" t-options='{"widget": "integer"}'/>
                    </td>
                    <td class="text-center" style="border: 1px solid black !important;">
                        <span t-field="o.employee_id.work_location_id"/>
                    </td>
                </tr>


            </table>

        </xpath>
        <xpath expr="//div[@id='total']" position="after">
            <table class="table table-sm">
                <thead class="o_black_border">
                    <tr>
                        <th>Name</th>
                        <!-- <th>Number of Hours</th>
                        <th>Number of Days</th> -->
                        <th class="text-right">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    <span t-foreach="o.worked_days_line_ids" t-as="worked_days">
                        <t t-if="worked_days.code != 'OUT'">
                            <tr style="color:none">
                                <td>
                                    <span t-field="worked_days.name"/>
                                </td>
                                <!-- <td>
                                    <span t-field="worked_days.number_of_hours"/>
                                </td>
                                <td>
                                    <span t-field="worked_days.number_of_days"/>
                                </td> -->
                                <td class="text-right">
                                    <span t-esc="worked_days.amount" digits="[42, 2]" t-options='{"widget": "monetary", "display_currency": o.company_id.currency_id}'/>
                                </td>
                            </tr>
                        </t>
                    </span>
                    <span t-foreach="o.line_ids.filtered(lambda line: line.appears_on_payslip)" t-as="line">
                        <t t-set="line_style"/>
                        <t t-set="line_class"/>
                        <t t-if="line.code == 'NET'">
                            <t t-set="line_style" t-value="'color:#875A7B;'"/>
                            <t t-set="line_class" t-value="'o_total o_border_bottom font-weight-bold'"/>
                        </t>
                        <t t-if="(line.code == 'BASIC') or (line.code == 'GROSS')">
                            <t t-set="line_style" t-value="'color:#00A09D;'"/>
                            <t t-set="line_class" t-value="'o_subtotal o_border_bottom'"/>
                        </t>
                        <tr t-att-class="line_class" t-att-style="line_style">
                            <td>
                                <span t-field="line.name"/>
                            </td>
                            <!-- <td></td>
                            <td>
                                <span t-if="line.quantity > 1" t-esc="line.quantity"/>
                            </td> -->
                            <td class="text-right">
                                <span t-esc="line.total" t-options='{"widget": "monetary", "display_currency": o.company_id.currency_id}' t-att-style="'color:#875A7B;' if line.total &lt; 0 else ''"/>
                            </td>
                        </tr>
                    </span>
                </tbody>
            </table>
        </xpath>



    </template>


</odoo>
