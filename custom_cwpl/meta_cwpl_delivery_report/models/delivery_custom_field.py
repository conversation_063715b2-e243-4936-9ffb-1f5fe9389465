# -*- coding: utf-8 -*-

from odoo import models, fields, api


class StockPickExtra(models.Model):
    _inherit = 'stock.picking'
    
    cwpl_contact = fields.Many2one(comodel_name="hr.employee",string="CWPL Contact",tracking=True)
    received_by=fields.Text(string="Received by", tracking=True)
    dg_plant_id = fields.Text(string="DG Plant ID",tracking=True)

class StockLocationExtra(models.Model):
    _inherit = 'stock.location'
    
    location_addr = fields.Text(string="Address",tracking=True)