from odoo import models,api,fields,_
from odoo.exceptions import ValidationError
import logging
from odoo.tools.float_utils import float_compare, float_is_zero, float_round

class StockMoveX(models.Model):
    _inherit = 'stock.move'

    do_remarks=fields.Char(string="Remarks")
class StockMoveLineX(models.Model):
    _inherit = 'stock.move.line'

    do_remarks=fields.Char(string="Remarks")

    def _get_aggregated_product_quantities(self, **kwargs):
        res = super(StockMoveLineX, self)._get_aggregated_product_quantities(**kwargs)
        for line_key in res:
            res[line_key]['do_remarks'] = ''
        for move_line in self:
            line_key = f'{move_line.product_id.id}_{move_line.product_id.display_name}_{move_line.move_id.description_picking or ""}_{move_line.product_uom_id.id}'
            
            if line_key in res:
                # Add do_remarks field
                if 'do_remarks' not in res[line_key]:
                    res[line_key]['do_remarks'] = move_line.do_remarks
                else:
                    # If multiple lines have remarks, concatenate them
                    res[line_key]['do_remarks'] += f", {move_line.do_remarks}" if move_line.do_remarks else ""
        for line_key in res:
            res[line_key]['do_remarks'] = res[line_key]['do_remarks'].rstrip(', ')

        return res