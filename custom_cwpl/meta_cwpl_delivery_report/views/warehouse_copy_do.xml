<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="warehouse_copy_do">
        <t t-call="web.html_container">
            <t t-foreach="docs.sudo()" t-as="o">
                <t t-call="web.external_layout">
                    <t t-set="o" t-value="o.with_context(lang=o.partner_id.lang or o.env.lang)" />
                    <t t-set="partner" t-value="o.partner_id or (o.move_lines and o.move_lines[0].partner_id) or False"/>
                    
                    <div class="row" style="border-bottom: 1px solid black;font-size:18px;">
                        <div class="col-12" style="text-align: left;">
                            <strong>Warehouse Copy</strong>
                        </div>
                    </div>
                    
                    <br/>

                    <div class="row" style="font-size:12px;">
                        <div class="col-9"></div>
                        <div class="col-3">
                            <div>
                                Do Ref: <span t-field="o.name"/>
                            </div>
                            <div>
                                Date: <span t-esc="o.create_date.strftime('%Y-%m-%d')"/>
                            </div>                            
                        </div>
                    </div>

                    <div class="information_block" style="border: 1px solid black;">

                        <div name="header1" style="border-bottom: 1px solid black;font-size:14px;">
                            <b>Customer Details</b>                            
                        </div>

                        <div class="row" style="font-size:12px;">
                            <div class="col-1"></div>
                            <div class="col-5" name="div_incoming_address">
                                <div t-if="partner" name="partner_header">
                                    <div>
                                        Customer's Name: <span t-field="partner.name"/>
                                    </div>
                                    <div>
                                        Address: <span t-field="partner.street"/>
                                        <span t-field="partner.street2"/><span t-field="partner.city"/><span t-field="partner.state_id.name"/><span t-field="partner.zip"/>
                                        <span t-field="partner.country_id.name"/>
                                    </div>
                                    <div>
                                        Phone: <span t-field="partner.phone"/>
                                    </div>
                                    <div>
                                        Contact Name: <span t-field="o.contact_name"/>
                                    </div>
                                    <div>
                                        <t t-if="o.select_con_del_partner.phone">
                                            Contact Phone: <span t-field="o.select_con_del_partner.phone"/>
                                        </t>
                                        <t t-elif="o.select_con_del_partner.mobile">
                                            Contact Mobile: <span t-field="o.select_con_del_partner.mobile"/>
                                        </t>
                                    </div>
                                </div>
                            </div>

                            <div class="col-5" name="div_internal_address">
                                <div>
                                    <t t-if="o.origin">
                                        Offer Ref: <span t-field="o.origin"/>
                                    </t>                                    
                                </div>
                                <div>
                                    <t t-if="o.cwpl_contact">
                                        Sales Person: <span t-field="o.cwpl_contact.name"/>
                                    </t>                                   
                                </div>
                                <div>
                                    <t t-if="o.cwpl_contact.job_id">
                                        Designation: <span t-field="o.cwpl_contact.job_id.name"/>
                                    </t>                                   
                                </div>
                                <div>
                                    <t t-if="o.cwpl_contact.mobile_phone">
                                        Mobile: <span t-field="o.cwpl_contact.mobile_phone"/>
                                    </t>                                   
                                </div>
                            </div>
                            <div class="col-1"></div>
                        </div>
                    </div>

                    <br/>
                    <br/>

                    <div name="site_informations" style="border: 1px solid black;">
                        
                        <div name="header2" style="font-size:14px;border-bottom: 1px solid black;">                            
                            <b>Site Details</b>                            
                        </div>

                        <div class="row" style="font-size:12px;">
                            <div class="col-1"></div>
                            <div class="col-5">
                                <div>
                                    <t t-if="o.customer_reference">
                                        Project Number: <span t-field="o.customer_reference.name"/> - <span t-field="o.customer_reference.partner_id.name"/>
                                    </t>
                                </div>
                                <div>
                                    <t t-if="o.delivery_location">
                                        Delivery Location: <span t-field="o.delivery_location"/>
                                    </t>
                                </div>
                            </div>
                            <div class="col-5"></div>
                            <div class="col-1"></div>
                        </div>
                    </div>
                    
                    <div class="page">
                        <br/>

                        <table class="table table-sm" t-if="o.state!='done'" name="stock_move_table">
                            <thead>
                                <tr>
                                    <th name="th_sm_product"><strong>Product</strong></th>
                                    <th name="th_sm_ordered"><strong>Ordered</strong></th>
                                    <th name="th_sm_quantity"><strong>Delivered</strong></th>
                                </tr>
                            </thead>
                            <tbody>
                                <t t-set="lines" t-value="o.move_lines.filtered(lambda x: x.product_uom_qty)"/>
                                <tr t-foreach="lines" t-as="move">
                                    <td>
                                        <span t-field="move.product_id"/>
                                        <p t-if="move.description_picking != move.product_id.name and move.description_picking != move.product_id.display_name">
                                            <span t-field="move.description_picking"/>
                                        </p>
                                    </td>
                                    <td>
                                        <span t-field="move.product_uom_qty"/>
                                        <span t-field="move.product_uom"/>
                                    </td>
                                    <td>
                                        <span t-field="move.quantity_done"/>
                                        <span t-field="move.product_uom"/>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <table class="table table-sm mt48" t-if="o.move_line_ids and o.state=='done'" name="stock_move_line_table">
                            <t t-set="has_serial_number" t-value="False"/>
                            <t t-set="has_serial_number" t-value="o.move_line_ids.mapped('lot_id')" groups="stock.group_lot_on_delivery_slip"/>
                            <thead>
                                <tr>
                                    <th name="th_sml_product"><strong>Product</strong></th>
                                    <t name="lot_serial" t-if="has_serial_number">
                                        <th>
                                            Lot/Serial Number
                                        </th>
                                    </t>
                                    <th name="th_sml_qty_ordered" class="text-center" t-if="not has_serial_number">
                                        <strong>Ordered</strong>
                                    </th>
                                    <th name="th_sml_quantity" class="text-center"><strong>Delivered</strong></th>
                                </tr>
                            </thead>
                            <tbody>
                            <!-- This part gets complicated with different use cases (additional use cases in extensions of this report):
                                    1. If serial numbers are used and set to print on delivery slip => print lines as is, otherwise group them by overlapping
                                        product + description + uom combinations
                                    2. If any packages are assigned => split products up by package (or non-package) and then apply use case 1 -->
                                <!-- If has destination packages => create sections of corresponding products -->
                                <t t-if="o.has_packages" name="has_packages">
                                    <t t-set="packages" t-value="o.move_line_ids.mapped('result_package_id')"/>
                                    <t t-foreach="packages" t-as="package">
                                        <t t-call="stock.stock_report_delivery_package_section_line"/>
                                        <t t-set="package_move_lines" t-value="o.move_line_ids.filtered(lambda l: l.result_package_id == package)"/>
                                        <!-- If printing lots/serial numbers => keep products in original lines -->
                                        <t t-if="has_serial_number">
                                            <tr t-foreach="package_move_lines" t-as="move_line">
                                                <t t-call="stock.stock_report_delivery_has_serial_move_line"/>
                                            </tr>
                                        </t>
                                        <!-- If not printing lots/serial numbers => merge lines with same product+description+uom -->
                                        <t t-else="">
                                            <t t-set="aggregated_lines" t-value="package_move_lines._get_aggregated_product_quantities(strict=True)"/>
                                            <t t-call="stock.stock_report_delivery_aggregated_move_lines"/>
                                        </t>
                                    </t>
                                    <!-- Make sure we do another section for package-less products if they exist -->
                                    <t t-set="move_lines" t-value="o.move_line_ids.filtered(lambda l: not l.result_package_id)"/>
                                    <t t-set="aggregated_lines" t-value="o.move_line_ids._get_aggregated_product_quantities(except_package=True)"/>
                                    <t t-if="move_lines or aggregated_lines" name="no_package_move_lines">
                                        <t t-call="stock.stock_report_delivery_no_package_section_line" name="no_package_section"/>
                                        <t t-if="has_serial_number">
                                            <tr t-foreach="move_lines" t-as="move_line">
                                                <t t-call="stock.stock_report_delivery_has_serial_move_line"/>
                                            </tr>
                                        </t>
                                        <t t-elif="aggregated_lines">
                                            <t t-call="stock.stock_report_delivery_aggregated_move_lines"/>
                                        </t>
                                    </t>
                                </t>
                                <!-- No destination packages -->
                                <t t-else="">
                                    <!-- If printing lots/serial numbers => keep products in original lines -->
                                    <t t-if="has_serial_number">
                                        <tr t-foreach="o.move_line_ids" t-as="move_line">
                                            <t t-call="stock.stock_report_delivery_has_serial_move_line"/>
                                        </tr>
                                    </t>
                                    <!-- If not printing lots/serial numbers => merge lines with same product -->
                                    <t t-else="" name="aggregated_move_lines">
                                        <t t-set="aggregated_lines" t-value="o.move_line_ids._get_aggregated_product_quantities()"/>
                                        <t t-call="stock.stock_report_delivery_aggregated_move_lines"/>
                                    </t>
                                </t>
                            </tbody>
                        </table>

                        <t t-set="backorders" t-value="o.backorder_ids.filtered(lambda x: x.state not in ('done', 'cancel'))"/>
                        <t t-if="o.backorder_ids and backorders">
                            <p class="mt-5">
                                <span>Remaining quantities not yet delivered:</span>
                            </p>
                            <table class="table table-sm" name="stock_backorder_table" style="table-layout: fixed;">
                                <thead>
                                    <tr>
                                        <th name="th_sb_product"><strong>Product</strong></th>
                                        <th/>
                                        <th name="th_sb_quantity" class="text-center"><strong>Quantity</strong></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <t t-foreach="backorders" t-as="backorder">
                                        <t t-set="bo_lines" t-value="backorder.move_lines.filtered(lambda x: x.product_uom_qty)"/>
                                        <tr t-foreach="bo_lines" t-as="bo_line">
                                            <td class="w-auto">
                                                <span t-field="bo_line.product_id"/>
                                                <p t-if="bo_line.description_picking != bo_line.product_id.name and bo_line.description_picking != bo_line.product_id.display_name">
                                                    <span t-field="bo_line.description_picking"/>
                                                </p>
                                            </td>
                                            <td/>
                                            <td class="text-center w-auto">
                                                <span t-field="bo_line.product_uom_qty"/>
                                                <span t-field="bo_line.product_uom"/>
                                            </td>
                                        </tr>
                                    </t>
                                </tbody>
                            </table>
                        </t>

                        <div class="row">
                            <div class="col-12">
                                <strong>Remarks:</strong>
                                <br/>
                                <span t-field="o.note"/>
                            </div>
                        </div>
                        <br/>
                        <br/>
                        
                        <div class="row">


                            <div class="col-4">
                                <strong>Received By:</strong>
                                <br/>
                                <br/>
                                <div style="border-top: 1px solid black;">
                                    <span><span t-field="o.received_by"/></span><br/>
                                    <!-- <span>Designation: </span><br/>
                                    <span>Contact: </span><br/> -->
                                </div>
                            </div>

                            <!-- <div class="col-4">
                                <strong>Warehouse Concern:</strong>
                                <br/>
                                <br/>
                                <div style="border-top: 1px solid black;">
                                    <span>Name: </span><br/>
                                    <span>Designation: </span><br/>
                                    <span>Contact: </span><br/>
                                </div>
                            </div>
                            
                            <div class="col-4">
                                <strong>Logistics Support:</strong>
                                <br/>
                                <br/>
                                <div style="border-top: 1px solid black;">
                                    <span>Name: </span><br/>
                                    <span>Designation: </span><br/>
                                    <span>Contact: </span><br/>
                                </div>
                            </div> -->
                        </div>

                        <br/>
                        <br/>
                        <div class="row" name="chatter_changes">
                            <div class="col-12">
                                <h5>
                                    Changes : 
                                </h5>

                                <t t-foreach="o.message_ids" t-as="chatter_details">
                                    <t t-if="chatter_details.message_type=='notification'">
                                        <span>
                                            <t t-if="chatter_details.author_id.name!='OdooBot'">
                                                <b>
                                                    <span t-field="chatter_details.author_id.name"/> :
                                                </b>
                                            </t>

                                            <t t-foreach="chatter_details.sudo().tracking_value_ids" t-as="track_value">
                                                <span>
                                                    <span t-field="track_value.field_desc"/>:
                                                    
                                                    <t t-if="track_value.old_value_char">
                                                        <span t-field="track_value.old_value_char"/> --&gt;
                                                    </t>
                                                    <t t-if="track_value.new_value_char">
                                                        <span t-field="track_value.new_value_char"/>,
                                                    </t>
                                                                                                      
                                                    <t t-if="track_value.old_value_text">
                                                        <span t-field="track_value.old_value_text"/> --&gt;
                                                    </t>
                                                    <t t-if="track_value.new_value_text">
                                                        <span t-field="track_value.new_value_text"/>,
                                                    </t>
                                                </span>
                                            </t>
                                            <br/>
                                        </span>
                                    </t>
                                </t>
                            </div>
                        </div>

                    </div>

                    <div t-attf-class="footer o_background_footer">
                        <div class="text-center" style="font-size:11px;">

                            <div class="row">
                                <div class="col-1"></div>
                                <div class="col-3" style="border:1px solid black;">
                                    <span>CWG-QM/FORM-0028</span>
                                </div>

                                <div class="col-1"></div>
                                <div class="col-2" style="border:1px solid black;">
                                    <span>Revision No: 00</span>                            
                                </div>                                

                                <div class="col-2"></div>
                                <div class="col-2 text-muted" style="border:1px solid black;">
                                    Page:
                                    <span class="page"/>
                                    of
                                    <span class="topage"/>
                                </div>

                                <div class="col-1"></div>
                            </div>

                            <div class="row">
                                <div class="col-12 text-center" t-if="o.company_id">
                                    <span t-field="o.company_id.street2"/>
                                    <span>,</span><span t-field="o.company_id.city"/> - <span t-field="o.company_id.zip"/><span>,</span><span t-field="o.company_id.country_id.name"/>|
                                    <span>p:</span><span t-field="o.company_id.phone"/>|
                                    <span>e:</span><span t-field="o.company_id.email"/>
                                </div>
                            </div>                                

                        </div>
                    </div>
                </t>
            </t>
         </t>
    </template>
</odoo>
