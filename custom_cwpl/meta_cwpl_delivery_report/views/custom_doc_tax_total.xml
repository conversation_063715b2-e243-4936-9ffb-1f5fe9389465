<?xml version="1.0" encoding="utf-8"?>
<odoo>        
    <record id="action_report_receipt" model="ir.actions.report">
        <field name="name">DO Warehouse Report</field>
        <field name="model">stock.picking</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">meta_cwpl_do_report.warehouse_copy_do</field>
        <field name="report_file">meta_cwpl_do_report.warehouse_copy_do</field>
        <field name="print_report_name">'DO Warehouse Copy - %s - %s' % (object.partner_id.name or '', object.name)</field>
        <field name="binding_model_id" ref="model_stock_picking"/>
        <field name="binding_type">report</field>
    </record>

    <record id="custom_do_report_paperformat" model="report.paperformat">
        <field name="name">Custom DO Paper Format</field>
        <field name="default" eval="True"/>
        <field name="format">custom</field>
        <field name="page_height">297</field>
        <field name="page_width">210</field>
        <field name="orientation">Portrait</field>
        <field name="margin_top">35</field>
        <field name="margin_bottom">18</field>
        <field name="margin_left">22</field>
        <field name="margin_right">5</field>
        <field name="header_line" eval="False"/>
        <field name="header_spacing">22</field>
        <field name="dpi">90</field>
    </record>
    <!-- <record id="action_report_do_gatepass" model="ir.actions.report">
        <field name="name">DO Gatepass Report</field>
        <field name="model">stock.picking</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">meta_cwpl_do_report.report_gate_pass_document</field>
        <field name="report_file">meta_cwpl_do_report.report_gate_pass_document</field>
        <field name="print_report_name">'DO Gate Pass - %s - %s' % (object.partner_id.name or '', object.name)</field>
        <field name="binding_model_id" ref="model_stock_picking"/>
        <field name="binding_type">report</field>
        <field name="paperformat_id" ref="meta_cwpl_do_report.custom_do_report_paperformat"/>
    </record>
    <record id="action_report_d_order" model="ir.actions.report">
        <field name="name">Delivery Order</field>
        <field name="model">stock.picking</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">meta_cwpl_do_report.report_d_order_document</field>
        <field name="report_file">meta_cwpl_do_report.report_d_order_document</field>
        <field name="print_report_name">'Delivery Order - %s - %s' % (object.partner_id.name or '', object.name)</field>
        <field name="binding_model_id" ref="model_stock_picking"/>
        <field name="binding_type">report</field>
        <field name="paperformat_id" ref="meta_cwpl_do_report.custom_do_report_paperformat"/>
    </record> -->

    <template id="custom_po_document_tax_totals">
        <t t-foreach="tax_totals['subtotals']" t-as="subtotal">
            <tr class="border-black o_subtotal">
                <!-- <td><strong t-esc="subtotal['name']"/></td> -->
                <td><strong>Total Price</strong></td>

                <td class="text-right">
                    <span
                        t-att-class="oe_subtotal_footer_separator"
                        t-esc="subtotal['formatted_amount']"
                    />
                </td>
            </tr>

            <t t-set="subtotal_to_show" t-value="subtotal['name']"/>
            <t t-call="account.tax_groups_totals"/>
        </t>

        <!--Total amount with all taxes-->
        <tr class="border-black o_total">
            <td><strong>Net PO Amount </strong></td>
            <td class="text-right">
                <span t-esc="tax_totals['formatted_amount_total']"/>
            </td>
        </tr>
    </template>

    <template id="custom_stock_report_delivery_aggregated_move_lines">
        <t t-set="row_count" t-value="0"/>
        <tr t-foreach="aggregated_lines" t-as="line">
            
            <t t-set="row_count" t-value="row_count + 1"/>
            <td style="font-size: 12px;text-align:center;border:1px solid black;padding-top:0 !important;padding-bottom:0 !important;">                                        
                <span t-esc="row_count"/>
            </td>

            <td style="font-size: 12px;border:1px solid black;padding-top:0 !important;padding-bottom:0 !important;">
                <span>
                    <span t-esc="aggregated_lines[line]['name']"/>
                    <t t-if="aggregated_lines[line]['product']">
                        <t t-set="product_tmpl" t-value="aggregated_lines[line]['product']['product_tmpl_id']"/>
                        <t t-if="product_tmpl['gen_brand']">
                            <br/>
                            <span>
                                BRAND : 
                                <span t-esc="product_tmpl['gen_brand']['name']"/>
                            </span>
                        </t>
                        <t t-if="product_tmpl['eng_brand']">
                            <br/>
                            <span>
                                ENGINE : 
                                <span t-esc="product_tmpl['eng_brand']['name']"/>
                            </span>
                        </t>
                        <t t-if="product_tmpl['alt_brand']">
                            <br/>
                            <span>
                                ALTERNATOR : 
                                <span t-esc="product_tmpl['alt_brand']['name']"/>
                            </span>
                            <br/>
                        </t>
                    </t>
                </span>
                            
                <p t-if="aggregated_lines[line]['description']">
                    <span t-esc="aggregated_lines[line]['description']"/>

                    <t t-if="aggregated_lines[line]['product']['product_tmpl_id']">
                        <t t-set="product_tmpl" t-value="aggregated_lines[line]['product']['product_tmpl_id']"/>
                        <t t-if="product_tmpl['gen_brand']">
                            <br/>
                            <span>
                                BRAND : 
                                <span t-esc="product_tmpl['gen_brand']['name']"/>
                            </span>
                        </t>
                        <t t-if="product_tmpl['eng_brand']">
                            <br/>
                            <span>
                                ENGINE : 
                                <span t-esc="product_tmpl['eng_brand']['name']"/>
                            </span>
                        </t>
                        <t t-if="product_tmpl['alt_brand']">
                            <br/>
                            <span>
                                ALTERNATOR : 
                                <span t-esc="product_tmpl['alt_brand']['name']"/>
                            </span>
                            <br/>
                        </t>
                    </t>
                </p>
            </td>
            <!-- <td class="text-center" name="move_line_aggregated_qty_ordered">
                <span t-esc="aggregated_lines[line]['qty_ordered']"
                    t-options="{'widget': 'float', 'decimal_precision': 'Product Unit of Measure'}"/>
                <span t-esc="aggregated_lines[line]['product_uom']"/>
            </td> -->
            <td class="text-center" name="move_line_aggregated_qty_done" style="font-size: 12px;border:1px solid black;padding-top:0 !important;padding-bottom:0 !important;">
                <t t-if="aggregated_lines[line]['qty_done']">
                    <span t-esc="aggregated_lines[line]['qty_done']"
                    t-options="{'widget': 'float', 'decimal_precision': 'Product Unit of Measure'}"/>
                    <!-- <span t-esc="aggregated_lines[line]['product_uom']"/> -->
                </t>
            </td>
            <td class="text-center" name="move_line_aggregated_qty_done" style="font-size: 12px;border:1px solid black;padding-top:0 !important;padding-bottom:0 !important;">
                <t t-if="aggregated_lines[line]['qty_done']">                    
                    <span t-esc="aggregated_lines[line]['product_uom']"/>
                </t>
            </td>

            <td style="border:1px solid black;"></td>
        </tr>
        
    </template>

    <template id="custom_stock_report_delivery_has_serial_move_line">
        
        <td style="font-size: 12px;padding-top:0 !important;padding-bottom:0 !important;">            
            <span t-field="move_line.product_id"/>
            <!-- this is an annoying workaround for the multiple types of descriptions (often auto-filled) that we do not want to print -->
            <!-- this makes it so we can pre-filter the descriptions in inherited templates since we cannot extend the standard "if" condition -->
            <!-- let's agree that pre-filtered descriptions will be set to "" -->
            <t t-if="not description and description != ''">
                <t t-set="description" t-value="move_line.move_id.description_picking"/>
            </t>
            <p t-if="description !='' and description != move_line.product_id.name">
                <span t-esc="description"/>
            </p>
        </td>
        <t t-if="has_serial_number" name="move_line_lot">
            <td style="font-size: 12px;padding-top:0 !important;padding-bottom:0 !important;"><span t-field="move_line.lot_id.name"/></td>
        </t>
        <td class="text-center" name="move_line_lot_qty_done" style="font-size: 12px;padding-top:0 !important;padding-bottom:0 !important;">
            <span t-field="move_line.qty_done"/>
            <!-- <span t-field="move_line.product_uom_id"/> -->
        </td>
        <td class="text-center" name="move_line_lot_qty_done" style="font-size: 12px;padding-top:0 !important;padding-bottom:0 !important;">            
            <span t-field="move_line.product_uom_id"/>            
        </td>
    </template>


</odoo>