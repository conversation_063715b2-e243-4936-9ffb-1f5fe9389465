<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <template inherit_id="stock.report_delivery_document" id="report_gate_pass_document">
        <xpath expr="." position="replace">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="o">
                    <t t-call="web.external_layout">
                        <t t-set="o" t-value="o.sudo().with_context(lang=o.sudo().partner_id.lang or o.sudo().env.lang)" />
                        <t t-set="partner" t-value="o.sudo().partner_id or (o.sudo().move_lines and o.sudo().move_lines[0].partner_id) or False"/> 
                        <div class="row" style="text-align:center;margin:0px;padding:0px;font-family: Arial, sans-serif;">
                            <div class="col-4"></div>
                            <div class="col-4" style="text-align:center;margin:0px;padding:0px;font-family: Arial, sans-serif;">
                                <b style="border-bottom:1px solid black;text-align:center;font-size:22px;">DO Gate Pass</b>
                            </div>
                            <div class="col-4"></div>
                        </div>
    
                        <div class="row" style="font-size: 14px;font-family: Arial, sans-serif;">
                            <div class="col-8"/>
                            <div class="col-4" style="font-size: 14px;">
                                <strong>Challan No: </strong>
                                <span t-field="o.sudo().name"/>
                                <br/>
                                <strong>Delivery Date : </strong>
                                <span t-field="o.sudo().create_date" t-options='{"widget": "date", "format": "dd/MM/yyyy"}'/>
                            </div>
                        </div>
    
                        <div class="row" style="border: 1px solid black;font-size: 14px;font-family: Arial, sans-serif;">
    
                            <div class="col-6" style="font-size: 14px;">                                
                                <strong>From:</strong>
                                <div t-if="o.sudo().company_id">
                                    <strong><span t-field="o.sudo().company_id.name"/></strong>
                                    <br/>
                                    <t t-if="o.sudo().company_id.street">
                                        <span t-field="o.sudo().company_id.street"/>                                    
                                    </t>
                                    <t t-if="o.sudo().company_id.street2">
                                        <span t-field="o.sudo().company_id.street2"/>                                    
                                    </t>
                                    <span t-field="o.sudo().company_id.city"/> - <span t-field="o.sudo().company_id.zip"/>
                                    <br/>
                                    <span>Tel: </span><span t-field="o.sudo().company_id.phone"/>
    
                                </div>
                            </div>
    
                            <div class="col-6" style="border-left: 1px solid black;font-size: 14px;font-family: Arial, sans-serif;">
                                <span><strong>To:</strong></span>
                                <t t-if="o.sudo().picking_type_id.code=='internal'">                               
                                    <br/>
                                    <strong><span t-field="o.sudo().location_dest_id.name"/></strong>
                                    <br/>
                                    <t t-if="o.sudo().location_dest_id.location_addr">
                                        <span t-field="o.sudo().location_dest_id.location_addr"/>,                                    
                                    </t>
                                    
                                    <br/>
                                    <!--<span>Tel:
                                        <t t-if="o.sudo().partner_id.phone">
                                            <span t-field="o.sudo().partner_id.phone"/>
                                        </t>
                                    </span>-->
                                </t>                          
                                <t t-else="">
                                    <div t-if="o.sudo().should_print_delivery_address()">
                                        
                                        <strong><span t-field="o.sudo().partner_id.name"/></strong>
                                        <br/>
                                        <t t-if="o.sudo().partner_id.street">
                                            <span t-field="o.sudo().partner_id.street"/>,                                    
                                        </t>
                                        <t t-if="o.sudo().partner_id.street2">
                                            <span t-field="o.sudo().partner_id.street2"/>,                                    
                                        </t>
                                        <t t-if="o.sudo().partner_id.state_id">
                                            <span t-field="o.sudo().partner_id.state_id.name"/>,                                    
                                        </t>
                                        <t t-if="o.sudo().partner_id.city">
                                            <span t-field="o.sudo().partner_id.city"/> - <span t-field="o.sudo().partner_id.zip"/>
                                        </t>
                                        <br/>
                                        <span>Tel: 
                                            <t t-if="o.sudo().partner_id.phone">
                                                <span t-field="o.sudo().partner_id.phone"/>
                                            </t>                                    
                                        </span>
                                        <!-- <div t-field="o.sudo().move_lines[0].partner_id"
                                            t-options='{"widget": "contact", "fields": ["address", "phone"], "no_marker": True, "phone_icons": True}'/> -->
                                    </div>
                                </t>
                                <span>
                                    PO/WO No:
                                    <t t-if="o.sudo().sale_id.so_customer_po_number">
                                        <span t-field="o.sudo().sale_id.so_customer_po_number"/>
                                    </t>
                                </span>
                                <br/>
                                <span>
                                    PO/WO Date:
                                    <t t-if="o.sudo().sale_id.so_customer_po_date">
                                        <span t-field="o.sudo().sale_id.so_customer_po_date" t-options="{&quot;widget&quot;: &quot;date&quot;, &quot;format&quot;: &quot;dd/MM/yyyy&quot;}"/>
                                    </t>
                                </span>
                            </div>
    
                        </div>
    
                        <div class="page" style="font-family: Arial, sans-serif;font-size: 14px;">
    
                            <br/>
    
                            <div class="row" style="border: 1px solid black;font-size: 14px;">
                                <div class="col-6" style="border-right: 1px solid black;font-size: 14px;">
                                    <strong>From Delivery:</strong>
                                    <br/>
                                    <span>
                                        Name:
                                        <t t-if="o.sudo().location_id">
                                            <span t-field="o.sudo().location_id.location_id.name"/>&#160;<span t-field="o.sudo().location_id.name"/>
                                        </t>
                                    </span>
                                    <br/>
                                    <span>
                                        Warehouse Address:
                                        <t t-if="o.sudo().location_id">
                                            <span t-field="o.sudo().location_id.location_addr"/>
                                        </t>
                                    </span>
                                    <br/>
                                    <span>
                                        Contact Person:
                                        <t t-if="o.sudo().cwpl_contact">
                                            <span t-field="o.sudo().cwpl_contact.name"/>
                                        </t>
                                    </span>
                                    <br/>
                                    <span>
                                        Contact Number:
                                        <t t-if="o.sudo().cwpl_contact.work_phone">
                                            <span t-field="o.sudo().cwpl_contact.work_phone"/>
                                        </t>
                                    </span>
                                </div>
                                <div class="col-6" style="font-size: 14px;">
                                    <strong>To Delivery:</strong>
                                    <br/>
                                    <span>
                                        <span>Site Address: </span>
                                        <t t-if="o.sudo().select_con_del_partner">
                                            <span t-field="o.sudo().select_con_del_partner"
                                            t-options='{"widget": "contact", "fields": ["address"], "no_marker": True}'/>
                                        </t>
                                    </span>
                                    <br/>
                                    <span>
                                        Site Concern:
                                        <t t-if="o.sudo().select_con_del_partner">
                                            <span t-field="o.sudo().select_con_del_partner.name"/>
                                        </t>
                                    </span>
                                    <br/>
                                    <span>
                                        Contact No:
                                        <t t-if="o.sudo().select_con_del_partner.phone">
                                            <span t-field="o.sudo().select_con_del_partner.phone"/>
                                        </t>
                                        <t t-elif="o.sudo().select_con_del_partner.mobile">
                                            <span t-field="o.sudo().select_con_del_partner.mobile"/>
                                        </t>
                                    </span>
                                    <br/>
                                    <span>
                                        Site ID:
                                        <t t-if="o.sudo().site_id">
                                            <span t-field="o.sudo().site_id"/>
                                        </t>
                                    </span>
                                </div>
                            </div>
                            <br/>          
                            <table class="table" t-if="o.sudo().state!='done'" name="stock_move_table" style="font-family: Arial, sans-serif;font-size:13px;border:1px solid black;width:103.4%;position:relative;left:-13px;">
                                <thead style="border:1px solid black;">
                                    <tr>
                                        <th name="th_sl" style="border:1px solid black;padding-top:0 !important;padding-bottom:0 !important;"><strong>SL No</strong></th>
                                        <th name="th_sm_product" style="border:1px solid black;padding-top:0 !important;padding-bottom:0 !important;"><strong>Description Of Goods</strong></th>
                                        <!-- <th name="th_sm_ordered"><strong>Ordered</strong></th> -->
                                        <th name="th_sm_quantity" style="border:1px solid black;padding-top:0 !important;padding-bottom:0 !important;"><strong>Delivered</strong></th>
                                        <th name="th_sm_unit" class="text-center" style="border:1px solid black;padding-top:0 !important;padding-bottom:0 !important;"><strong>Unit</strong></th>
                                        <th name="th_sm_remarks" style="border:1px solid black;padding-top:0 !important;padding-bottom:0 !important;"><strong>Remarks</strong></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <t t-set="lines" t-value="o.sudo().move_lines.filtered(lambda x: x.product_uom_qty)"/>
                                    <t t-set="row_count" t-value="0"/>
                                    <tr t-foreach="lines" t-as="move" style="padding-top:0 !important;padding-bottom:0 !important;">
                                        
                                        <t t-set="row_count" t-value="row_count + 1"/>
                                        <td style="font-size: 12px;border:1px solid black;padding-top:0 !important;padding-bottom:0 !important;">                                        
                                            <span t-esc="row_count"/>
                                        </td>
    
                                        <td style="font-size: 12px;border:1px solid black;padding-top:0 !important;padding-bottom:0 !important;">
                                            <span t-field="move.product_id"/>
                                            <p t-if="move.description_picking != move.product_id.name and move.description_picking != move.product_id.display_name">
                                                <span t-field="move.description_picking"/>
                                            </p>
                                            <t t-if="move.product_id.product_tmpl_id.gen_brand">
                                                <br/>
                                                <span>
                                                    BRAND : 
                                                    <b><span t-field="move.product_id.product_tmpl_id.gen_brand.name"/></b>
                                                </span>
                                            </t>
                                            <t t-if="move.product_id.product_tmpl_id.eng_brand">
                                                <br/>
                                                <span>
                                                    ENGINE : 
                                                    <b><span t-field="move.product_id.product_tmpl_id.eng_brand.name"/></b>
                                                </span>
                                            </t>
                                            <t t-if="move.product_id.product_tmpl_id.alt_brand">
                                                <br/>
                                                <span>
                                                    ALTERNATOR : 
                                                    <b><span t-field="move.product_id.product_tmpl_id.alt_brand.name"/></b>
                                                </span>
                                                <br/>
                                                
                                            </t>
                                        </td>
                                        
                                        
                                        <td style="font-size: 12px;border:1px solid black;padding-top:0 !important;padding-bottom:0 !important;">                                        
                                            <span t-field="move.quantity_done"/>
                                            <!-- <span t-field="move.product_uom"/> -->
                                        </td>
                                        <td style="font-size: 12px;border:1px solid black;padding-top:0 !important;padding-bottom:0 !important;">
                                            <!-- <span t-field="move.product_uom_qty"/> -->
                                            <span t-field="move.product_uom"/>
                                        </td>
    
                                        <td style="font-size: 12px;border:1px solid black;padding-top:0 !important;padding-bottom:0 !important;">
                                            <span t-field="move.do_remarks"/>                                    
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <br/>
    
                            <table class="table" t-if="o.sudo().move_line_ids and o.sudo().state=='done'" name="stock_move_line_table" style="font-family: Arial, sans-serif;font-size:15px;border:1px solid black;width:103.4%;position:relative;left:-13px;">
                                <t t-set="has_serial_number" t-value="False"/>
                                <t t-set="has_serial_number" t-value="o.sudo().move_line_ids.mapped('lot_id')" groups="stock.group_lot_on_delivery_slip"/>
                                <thead>
                                    <tr style="border:1px solid black;">
                                        <th name="th_sl" style="border:1px solid black;padding-top:0 !important;padding-bottom:0 !important;"><strong>SL No</strong></th>
                                        <th name="th_sml_product" style="border:1px solid black;padding-top:0 !important;padding-bottom:0 !important;"><strong>Description Of Goods</strong></th>
                                        <t name="lot_serial" t-if="has_serial_number">
                                            <th style="border:1px solid black;padding-top:0 !important;padding-bottom:0 !important;">
                                                Lot/Serial Number
                                            </th>
                                        </t>
                                        
                                        <!-- <th name="th_sml_qty_ordered" class="text-center" t-if="not has_serial_number">
                                            <strong>Ordered</strong>
                                        </th> -->
    
                                        <th name="th_sml_quantity" class="text-center" style="border:1px solid black;padding-top:0 !important;padding-bottom:0 !important;"><strong>Delivered</strong></th>
                                        <th name="th_sm_unit" class="text-center" style="border:1px solid black;padding-top:0 !important;padding-bottom:0 !important;"><strong>Unit</strong></th>
    
                                        <th name="th_sml_remarks" class="text-center" style="border:1px solid black;padding-top:0 !important;padding-bottom:0 !important;"><strong>Remarks</strong></th>
                                    </tr>
                                </thead>
    
                                <tbody style="font-family: Arial, sans-serif;font-size:14px;border:1px solid black;">                        
                                    <t t-if="o.sudo().has_packages" name="has_packages">
                                        <t t-set="packages" t-value="o.sudo().move_line_ids.mapped('result_package_id')"/>
                                        
                                        <t t-foreach="packages" t-as="package">
                                            <t t-call="stock.stock_report_delivery_package_section_line"/>
                                            <t t-set="package_move_lines" t-value="o.sudo().move_line_ids.filtered(lambda l: l.result_package_id == package)"/>                                        
                                            <!-- If printing lots/serial numbers => keep products in original lines -->
                                            <t t-if="has_serial_number">
                                                <t t-set="row_count" t-value="0"/>
                                                <tr t-foreach="package_move_lines" t-as="move_line" style="padding-top:0 !important;padding-bottom:0 !important;">
                                                    
                                                    <t t-set="row_count" t-value="row_count + 1"/>
                                                    <td style="font-size: 14px">                                        
                                                        <span t-esc="row_count"/>
                                                    </td>
                                                    <t t-call="meta_cwpl_do_report.custom_stock_report_delivery_has_serial_move_line"/>
                                                    <td>
                                                        <span t-field="move_line.do_remarks"/>                                    
                                                    </td>
                                                </tr>
                                            </t>
    
                                            <!-- If not printing lots/serial numbers => merge lines with same product+description+uom -->
                                            <t t-else="">
                                                <t t-set="aggregated_lines" t-value="package_move_lines._get_aggregated_product_quantities(strict=True)"/>
                                                <t t-call="meta_cwpl_do_report.custom_stock_report_delivery_aggregated_move_lines"/>
    
                                                <!-- <td class="text-left">
                                                    <span t-esc="o.note"/>
                                                </td> -->
                                            </t>
                                        </t>
    
                                        <!-- Make sure we do another section for package-less products if they exist -->
                                        <t t-set="move_lines" t-value="o.sudo().move_line_ids.filtered(lambda l: not l.result_package_id)"/>
    
                                        <t t-set="aggregated_lines" t-value="o.sudo().move_line_ids._get_aggregated_product_quantities(except_package=True)"/>
    
                                        <t t-if="move_lines or aggregated_lines" name="no_package_move_lines">
                                            <t t-call="stock.stock_report_delivery_no_package_section_line" name="no_package_section"/>
                                            <t t-if="has_serial_number">
                                                <tr t-foreach="move_lines" t-as="move_line">
                                                    <t t-call="stock.stock_report_delivery_has_serial_move_line"/>
                                                    
                                                    <td class="text-left">
                                                        <span t-esc="o.note"/>
                                                    </td>
                                                </tr>
                                            </t>
                                            <t t-elif="aggregated_lines">
                                                <t t-call="meta_cwpl_do_report.custom_stock_report_delivery_aggregated_move_lines"/>
                                                
                                                <!-- <td class="text-left">
                                                    <span t-esc="o.note"/>
                                                </td> -->
                                            </t>
                                        </t>
    
                                    </t>
                                    <!-- No destination packages -->
                                    <t t-else="">
                                        <!-- If printing lots/serial numbers => keep products in original lines -->
                                        <t t-if="has_serial_number">
                                            <tr t-foreach="o.sudo().move_line_ids" t-as="move_line">
                                                <t t-call="stock.stock_report_delivery_has_serial_move_line"/>
                                                <td>
                                                    <span t-field="move_line.do_remarks"/>                                    
                                                </td>
                                            </tr>
                                        </t>
                                        <!-- If not printing lots/serial numbers => merge lines with same product -->
                                        <t t-else="" name="aggregated_move_lines">
                                            <t t-set="aggregated_lines" t-value="o.sudo().move_line_ids._get_aggregated_product_quantities()"/>
                                            <t t-call="meta_cwpl_do_report.custom_stock_report_delivery_aggregated_move_lines"/>
                                            <!-- <td class="text-left">
                                                <span t-esc="o.note"/>
                                            </td> -->
                                        </t>
                                    </t>
                                </tbody>
                            </table>
                            
                            <br/>
                            <div class="row" style="border: 1px solid black;">
    
                                <div class="col-4" style="border-right: 1px solid black;font-size: 14px;">
                                    <strong>Warehouse Concern:</strong>
                                    <br/>
                                    <br/>
                                    <br/>
                                    <br/>
                                    <div style="border-top: 1px solid black;font-size: 14px;">
                                        <span>Name: </span><br/>
                                        <span>Designation: </span><br/>
                                        <span>Contact: </span><br/>
                                    </div>
                                </div>
                                
                                <div class="col-4" style="font-size: 14px;">
                                    <strong>Logistics Support:</strong>
                                    <br/>
                                    <br/>
                                    <br/>
                                    <br/>
                                    <div style="border-top: 1px solid black;font-size: 14px;">
                                        <span>Name: </span><br/>
                                        <span>Designation: </span><br/>
                                        <span>Contact: </span><br/>
                                    </div>
                                </div>
    
                                <div class="col-4" style="border-left: 1px solid black;font-size: 14px;">
                                    <strong>Received By:</strong>
                                    <br/>
                                    <br/>
                                    <br/>
                                    <br/>
                                    <div style="border-top: 1px solid black;font-size: 14px;">
                                        <span>Name: </span><br/>
                                        <span>Designation: </span><br/>
                                        <span>Contact: </span><br/>
                                    </div>
                                </div>
    
                            </div>
                            <br/>
    
                            <div t-if="o.sudo().signature" class="mt32 ml64 mr4" name="signature">
                                <div class="offset-8">
                                    <strong>Signature</strong>
                                </div>
                                <div class="offset-8">
                                    <img t-att-src="image_data_uri(o.sudo().signature)" style="max-height: 4cm; max-width: 8cm;"/>
                                </div>
                                <div class="offset-8 text-center">
                                    <p t-field="o.sudo().partner_id.name"/>
                                </div>
                            </div>
                            
                        </div>
                        
                        <div t-attf-class="footer o_background_footer" style="border-top:1px solid black !important;margin:0 !important; padding:0 !important;">
                            <div class="text-center" style="font-size:11px;margin:0 !important; padding:0 !important;">
                                <p style="margin:0 !important; padding:0 !important;font-size:12px !important;color:black !important;text-align:left !important;">This is a system generated document.</p>
                                <div class="row">
                                    <div class="col-12 text-center" t-if="o.sudo().company_id">
                                        <span t-field="o.sudo().company_id.street2"/>
                                        <span>,</span><span t-field="o.sudo().company_id.city"/> - <span t-field="o.sudo().company_id.zip"/><span>,</span><span t-field="o.sudo().company_id.country_id.name"/>|
                                        <span>p:</span><span t-field="o.sudo().company_id.phone"/>|
                                        <span>e:</span><span t-field="o.sudo().company_id.email"/>
                                    </div>
                                </div>                                
                                <div class="row">
                                    <div class="col-1"></div>
                                    <div class="col-3" style="border:1px solid black;">
                                        <span>CWG-QM/FORM-0033</span>
                                    </div>
    
                                    <div class="col-1"></div>
                                    <div class="col-2" style="border:1px solid black;">
                                        <span>Revision No: 00</span>                            
                                    </div>                                
    
                                    <div class="col-2"></div>
                                    <div class="col-2 text-muted" style="border:1px solid black;">
                                        Page:
                                        <span class="page"/>
                                        of
                                        <span class="topage"/>
                                    </div>
    
                                    <div class="col-1"></div>
                                </div>
    
    
                            </div>
                        </div>
                        
                    </t>
                </t>
            </t>
        </xpath>
    </template>
</odoo>